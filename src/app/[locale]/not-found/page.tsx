import { NextIntlClientProvider } from 'next-intl';
import { notFound } from 'next/navigation';
import { routing } from '@/i18n/routing';
import { getMessages } from '@/i18n/routing';
import NotFoundPage from '@/components/NotFoundPage';
import { Metadata } from 'next';

interface NotFoundPageProps {
  params: Promise<{ locale: string }>;
}

// Generate metadata for the 404 page
export async function generateMetadata({ params }: NotFoundPageProps): Promise<Metadata> {
  const { locale } = await params;
  
  // Validate locale
  if (!routing.locales.includes(locale as any)) {
    notFound();
  }

  const isEnglish = locale === 'en';
  
  return {
    title: isEnglish ? 'Page Not Found - Bili Tool' : '页面未找到 - 哔哩工具箱',
    description: isEnglish 
      ? 'The page you are looking for could not be found. Explore our comprehensive collection of online tools.'
      : '您访问的页面未找到。探索我们全面的在线工具集合。',
    robots: 'noindex, nofollow', // Don't index 404 pages
  };
}

/**
 * Dedicated not-found page route.
 * This provides a user-friendly 404 experience with proper translations and navigation.
 */
export default async function NotFoundPageRoute({ params }: NotFoundPageProps) {
  const { locale } = await params;
  
  // Validate locale
  if (!routing.locales.includes(locale as any)) {
    notFound();
  }

  // Get messages for the locale
  const messages = await getMessages(locale);

  return (
    <NextIntlClientProvider messages={messages} locale={locale}>
      <NotFoundPage locale={locale} />
    </NextIntlClientProvider>
  );
}
