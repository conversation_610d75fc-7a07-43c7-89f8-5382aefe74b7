'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Search, Download, Copy, RefreshCw } from 'lucide-react';

export default function WhoisPage() {
  const [domain, setDomain] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!domain) {
      setError('请输入域名');
      return;
    }
    
    // 简单的域名格式验证
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/;
    if (!domainRegex.test(domain)) {
      setError('请输入有效的域名');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/whois', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ domain }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '查询失败');
      }

      setResult(data.data);
      setLoading(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : '查询失败，请稍后重试');
      setLoading(false);
    }
  };

  const copyToClipboard = () => {
    if (result) {
      navigator.clipboard.writeText(result);
      // 创建临时通知
      const notification = document.createElement('div');
      notification.className = 'fixed bottom-4 right-4 p-4 rounded-lg shadow-lg bg-primary text-white z-50';
      notification.innerHTML = `
        <h4 class="font-medium">复制成功</h4>
        <p class="text-sm">已复制查询结果到剪贴板</p>
      `;
      document.body.appendChild(notification);
      
      // 3秒后移除通知
      setTimeout(() => {
        notification.remove();
      }, 3000);
    }
  };

  const refreshQuery = () => {
    if (domain) {
      handleSubmit({ preventDefault: () => {} } as React.FormEvent);
    }
  };

  const downloadResult = () => {
    if (result) {
      const blob = new Blob([result], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `whois-${domain}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-5xl mx-auto">
        {/* 返回按钮 */}
        <button 
          onClick={() => router.back()}
          className="mb-6 flex items-center gap-2 text-primary hover:underline"
        >
          <ArrowLeft size={16} />
          <span>返回工具列表</span>
        </button>

        {/* 工具标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Whois查询</h1>
          <p className="text-muted-foreground mt-2">
            查询域名注册信息，包括所有者、注册商、注册日期和到期日期等详细信息
          </p>
        </div>

        {/* 查询表单 */}
        <div className="bg-card border border-border rounded-lg p-6 mb-6">
          <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <label htmlFor="domain" className="block text-sm font-medium mb-2">
                域名
              </label>
              <div className="relative">
                <input
                  id="domain"
                  type="text"
                  placeholder="wenhaofree.com"
                  value={domain}
                  onChange={(e) => setDomain(e.target.value)}
                  className="w-full h-10 pl-4 pr-10 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
              {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
            </div>
            <div className="flex items-end">
              <button
                type="submit"
                disabled={loading}
                className="h-10 px-6 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-2 disabled:opacity-70"
              >
                {loading ? (
                  <>
                    <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></span>
                    <span>查询中...</span>
                  </>
                ) : (
                  <>
                    <Search size={16} />
                    <span>查询</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>

        {/* 结果显示 */}
        {result && (
          <div className="bg-card border border-border rounded-lg">
            <div className="p-4 border-b border-border flex items-center justify-between">
              <h3 className="font-medium">查询结果</h3>
              <div className="flex items-center gap-2">
                <button 
                  onClick={refreshQuery}
                  className="p-2 rounded hover:bg-accent"
                  title="刷新"
                >
                  <RefreshCw size={16} />
                </button>
                <button 
                  onClick={copyToClipboard}
                  className="p-2 rounded hover:bg-accent"
                  title="复制到剪贴板"
                >
                  <Copy size={16} />
                </button>
                <button 
                  onClick={downloadResult}
                  className="p-2 rounded hover:bg-accent"
                  title="下载结果"
                >
                  <Download size={16} />
                </button>
              </div>
            </div>
            <pre className="p-6 overflow-auto bg-accent/50 whitespace-pre-wrap rounded-b-lg">
              {result}
            </pre>
          </div>
        )}

        {/* 详细使用指南 */}
        <div className="mt-8 space-y-8">
          {/* 使用说明 */}
          <div>
            <h2 className="text-2xl font-semibold mb-4">使用说明</h2>
            <div className="bg-card border border-border rounded-lg p-6">
              <ol className="list-decimal list-inside space-y-3">
                <li className="text-base">在输入框中输入您想要查询的域名（如：wenhaofree.com）</li>
                <li className="text-base">点击"查询"按钮获取域名的Whois注册信息</li>
                <li className="text-base">查询结果将显示域名的所有者、注册商、创建日期、到期日期等信息</li>
                <li className="text-base">您可以使用工具栏上的按钮复制或下载查询结果</li>
              </ol>
              <div className="mt-6 p-4 bg-accent/50 rounded-lg">
                <p className="text-sm text-muted-foreground">
                  <strong>提示：</strong> Whois信息可能会因隐私保护而被部分隐藏。某些顶级域名可能有不同的Whois格式。
                </p>
              </div>
            </div>
          </div>

          {/* Whois信息详解 */}
          <div>
            <h2 className="text-2xl font-semibold mb-4">Whois信息详解</h2>
            <div className="bg-card border border-border rounded-lg p-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">基本信息字段</h3>
                  <ul className="space-y-2 text-sm">
                    <li><strong>域名（Domain Name）：</strong>查询的域名</li>
                    <li><strong>注册商（Registrar）：</strong>域名注册服务提供商</li>
                    <li><strong>注册日期（Creation Date）：</strong>域名首次注册时间</li>
                    <li><strong>到期日期（Expiration Date）：</strong>域名到期时间</li>
                    <li><strong>更新日期（Updated Date）：</strong>最后修改时间</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-3">联系信息</h3>
                  <ul className="space-y-2 text-sm">
                    <li><strong>注册人（Registrant）：</strong>域名所有者信息</li>
                    <li><strong>管理联系人（Admin Contact）：</strong>域名管理员信息</li>
                    <li><strong>技术联系人（Tech Contact）：</strong>技术负责人信息</li>
                    <li><strong>计费联系人（Billing Contact）：</strong>付费联系人信息</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* 实际应用场景 */}
          <div>
            <h2 className="text-2xl font-semibold mb-4">实际应用场景</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-card border border-border rounded-lg p-4">
                <h3 className="font-medium mb-2">🔍 域名调研</h3>
                <p className="text-sm text-muted-foreground">
                  在注册域名前查看是否已被注册，了解竞争对手的域名信息
                </p>
              </div>
              <div className="bg-card border border-border rounded-lg p-4">
                <h3 className="font-medium mb-2">🛡️ 安全审计</h3>
                <p className="text-sm text-muted-foreground">
                  验证域名所有权，检查是否存在可疑的域名注册活动
                </p>
              </div>
              <div className="bg-card border border-border rounded-lg p-4">
                <h3 className="font-medium mb-2">📧 联系信息</h3>
                <p className="text-sm text-muted-foreground">
                  获取域名所有者的联系方式，用于商务合作或法律事务
                </p>
              </div>
              <div className="bg-card border border-border rounded-lg p-4">
                <h3 className="font-medium mb-2">📊 市场分析</h3>
                <p className="text-sm text-muted-foreground">
                  分析域名注册趋势，了解行业竞争格局
                </p>
              </div>
              <div className="bg-card border border-border rounded-lg p-4">
                <h3 className="font-medium mb-2">⚖️ 法律维权</h3>
                <p className="text-sm text-muted-foreground">
                  收集证据用于域名争议解决或商标侵权案件
                </p>
              </div>
              <div className="bg-card border border-border rounded-lg p-4">
                <h3 className="font-medium mb-2">🔄 域名转移</h3>
                <p className="text-sm text-muted-foreground">
                  确认域名状态，准备域名转移或续费操作
                </p>
              </div>
            </div>
          </div>

          {/* 常见问题 */}
          <div>
            <h2 className="text-2xl font-semibold mb-4">常见问题</h2>
            <div className="space-y-4">
              <div className="bg-card border border-border rounded-lg p-4">
                <h3 className="font-medium mb-2">为什么有些域名的Whois信息显示为隐私保护？</h3>
                <p className="text-sm text-muted-foreground">
                  许多域名注册商提供隐私保护服务，将真实的注册信息替换为代理信息，以保护域名所有者的隐私。这是完全合法的做法。
                </p>
              </div>
              <div className="bg-card border border-border rounded-lg p-4">
                <h3 className="font-medium mb-2">Whois信息的准确性如何？</h3>
                <p className="text-sm text-muted-foreground">
                  Whois信息由域名注册商维护，通常是准确的。但是，信息可能不会实时更新，建议以最新的查询结果为准。
                </p>
              </div>
              <div className="bg-card border border-border rounded-lg p-4">
                <h3 className="font-medium mb-2">可以查询所有类型的域名吗？</h3>
                <p className="text-sm text-muted-foreground">
                  大多数通用顶级域名（gTLD）和国家代码顶级域名（ccTLD）都支持Whois查询，但某些特殊域名可能有不同的查询方式。
                </p>
              </div>
              <div className="bg-card border border-border rounded-lg p-4">
                <h3 className="font-medium mb-2">查询频率有限制吗？</h3>
                <p className="text-sm text-muted-foreground">
                  为了防止滥用，我们对查询频率有一定限制。如果您需要大量查询，请联系我们了解API服务。
                </p>
              </div>
            </div>
          </div>

          {/* 相关工具推荐 */}
          <div>
            <h2 className="text-2xl font-semibold mb-4">相关工具推荐</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-card border border-border rounded-lg p-4 hover:shadow-md transition-shadow">
                <h3 className="font-medium mb-2">🌐 DNS查询</h3>
                <p className="text-sm text-muted-foreground mb-3">
                  查询域名的DNS记录，了解域名解析配置
                </p>
                <button className="text-primary text-sm hover:underline">
                  立即使用 →
                </button>
              </div>
              <div className="bg-card border border-border rounded-lg p-4 hover:shadow-md transition-shadow">
                <h3 className="font-medium mb-2">📍 IP定位</h3>
                <p className="text-sm text-muted-foreground mb-3">
                  查询IP地址的地理位置和ISP信息
                </p>
                <button className="text-primary text-sm hover:underline">
                  立即使用 →
                </button>
              </div>
              <div className="bg-card border border-border rounded-lg p-4 hover:shadow-md transition-shadow">
                <h3 className="font-medium mb-2">🔗 域名信息</h3>
                <p className="text-sm text-muted-foreground mb-3">
                  全面分析域名的技术信息和安全状态
                </p>
                <button className="text-primary text-sm hover:underline">
                  立即使用 →
                </button>
              </div>
            </div>
          </div>

          {/* 技术说明 */}
          <div>
            <h2 className="text-2xl font-semibold mb-4">技术说明</h2>
            <div className="bg-card border border-border rounded-lg p-6">
              <h3 className="text-lg font-medium mb-3">Whois协议简介</h3>
              <p className="text-muted-foreground mb-4">
                Whois是一个用于查询域名注册信息的协议，最初在RFC 954中定义。它允许用户查询域名、IP地址和自治系统的注册信息。
              </p>

              <h3 className="text-lg font-medium mb-3">查询流程</h3>
              <ol className="list-decimal list-inside space-y-2 text-muted-foreground mb-4">
                <li>客户端向Whois服务器发送查询请求</li>
                <li>服务器查找相关的注册信息</li>
                <li>返回格式化的注册数据</li>
                <li>客户端解析并显示结果</li>
              </ol>

              <h3 className="text-lg font-medium mb-3">数据来源</h3>
              <p className="text-muted-foreground">
                我们的Whois查询工具连接到权威的Whois服务器，包括各大域名注册局和注册商的官方数据库，确保信息的准确性和时效性。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 