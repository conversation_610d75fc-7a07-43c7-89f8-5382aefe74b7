'use client';

import React, { useState, useEffect, useRef, createRef, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from 'next-themes';
import { useRouter, usePathname } from 'next/navigation';
import dynamic from 'next/dynamic';
import { 
  ChevronLeft, 
  ChevronRight, 
  ChevronDown,
  Star, 
  Clock, 
  Grid, 
  List, 
  Search,
  Filter,
  Plus,
  Heart,
  Share2,
  MoreVertical
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  Tool,
  Notification,
  tools,
  sidebarMenu,
  getFilteredToolsByCategory,
  toggleToolFavorite,
  isToolDeveloped,
  createNotification,
  getSubMenuItems,
  getToolsForSubCategory,
  getToolsBySubCategory,
  getLocalizedToolName,
  getLocalizedToolDescription,
  getLocaleFromPathname,
  getLocalizedCategoryName,
  getLocalizedMenuItemName
} from '@/lib/tools';
import {
  getCachedTools,
  cacheTools,
  getCachedFavorites,
  updateCachedFavorites,
  getCachedRecentTools,
  updateCachedRecentTools
} from '@/lib/tools/cache';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';

// 动态导入较大的组件
const QuickNavMenu = dynamic(() => import('@/components/QuickNavMenu'), {
  loading: () => <div className="w-12 h-12 rounded-full bg-primary/20 animate-pulse" />
});

// 工具卡片组件
const ToolCard = React.memo(({ 
  tool, 
  index, 
  isHighlighted, 
  currentLocale,
  onToolClick,
  onToggleFavorite 
}: {
  tool: Tool;
  index: number;
  isHighlighted: boolean;
  currentLocale: string;
  onToolClick: (toolId: string, event: React.MouseEvent) => void;
  onToggleFavorite: (toolId: string) => void;
}) => {
  const animationDelay = `${index * 0.05}s`;
  const localizedName = getLocalizedToolName(tool, currentLocale);
  const localizedDescription = getLocalizedToolDescription(tool, currentLocale);
  
  return (
    <motion.div
      key={tool.id}
      id={tool.id}
      className={cn(
        "group relative flex flex-col rounded-2xl bg-card text-card-foreground p-6 shadow-sm hover:shadow-lg transition-all duration-300 border border-border cursor-pointer overflow-hidden focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
        "animate-fade-up hover:-translate-y-1"
      )}
      style={{ animationDelay }}
      animate={{
        scale: isHighlighted ? [1, 1.05, 1] : 1,
        transition: {
          duration: isHighlighted ? 0.5 : 0.2
        }
      }}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
      onClick={(e) => onToolClick(tool.id, e)}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          // Create a synthetic mouse event for keyboard navigation
          const syntheticEvent = {
            ...e,
            currentTarget: e.currentTarget,
            target: e.target,
            stopPropagation: e.stopPropagation.bind(e),
            preventDefault: e.preventDefault.bind(e)
          } as unknown as React.MouseEvent;
          onToolClick(tool.id, syntheticEvent);
        }
      }}
      tabIndex={0}
      role="button"
      aria-label={`${localizedName} - ${localizedDescription}`}
      layout
    >
      {/* Highlight overlay */}
      {isHighlighted && (
        <motion.div
          className="absolute inset-0 flex items-center justify-center z-10 pointer-events-none bg-primary/10 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <div className="bg-primary text-primary-foreground px-4 py-2 rounded-full shadow-lg animate-pulse font-medium">
            {currentLocale === 'en' ? 'Located' : '已定位到此工具'}
          </div>
        </motion.div>
      )}

      {/* Gradient background overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

      {/* Tool icon with enhanced styling */}
      <div className="relative mb-4 flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 text-primary group-hover:from-primary/20 group-hover:to-primary/10 transition-all duration-300">
        <div className="text-xl">
          {tool.icon}
        </div>
      </div>

      {/* Content */}
      <div className="relative flex-1">
        <h3 className="mb-3 text-lg font-semibold text-foreground group-hover:text-primary transition-colors duration-200">
          {localizedName}
        </h3>

        <p className="text-sm text-muted-foreground line-clamp-3 leading-relaxed">
          {localizedDescription}
        </p>
      </div>

      {/* Action buttons */}
      <div className="absolute top-4 right-4 flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-all duration-200 tool-actions">
        <button
          onClick={(e) => {
            e.stopPropagation();
            onToggleFavorite(tool.id);
          }}
          className={`p-2 rounded-lg transition-all duration-200 ${
            tool.isFavorite
              ? 'text-red-500 bg-red-50 hover:bg-red-100'
              : 'text-muted-foreground hover:text-red-500 hover:bg-red-50'
          }`}
        >
          <Heart size={16} fill={tool.isFavorite ? 'currentColor' : 'none'} />
        </button>
        <button
          className="p-2 rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent transition-all duration-200"
          onClick={(e) => e.stopPropagation()}
        >
          <Share2 size={16} />
        </button>
        <button
          className="p-2 rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent transition-all duration-200"
          onClick={(e) => e.stopPropagation()}
        >
          <MoreVertical size={16} />
        </button>
      </div>

      {/* Status indicator */}
      {!isToolDeveloped(tool.id) && (
        <div className="absolute bottom-4 left-6">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400">
            {currentLocale === 'en' ? 'Coming Soon' : '即将推出'}
          </span>
        </div>
      )}
    </motion.div>
  );
});

ToolCard.displayName = 'ToolCard';

export default function Home() {
  // 状态管理
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [highlightedTool, setHighlightedTool] = useState<string | null>(null);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [activeTool, setActiveTool] = useState<string | null>(null);
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [isQuickNavVisible, setIsQuickNavVisible] = useState(false);
  const [mounted, setMounted] = useState(false);
  
  // Hooks
  const { theme, setTheme } = useTheme();
  const router = useRouter();
  const pathname = usePathname();
  const currentLocale = getLocaleFromPathname(pathname);
  const highlightTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // 工具和分类引用
  const toolRefs = useRef<Record<string, { ref: React.RefObject<any> }>>({});
  const categoryRefs = useRef<Record<string, { ref: React.RefObject<HTMLDivElement> }>>({});
  
  // 初始化缓存
  useEffect(() => {
    const cachedTools = getCachedTools();
    if (cachedTools) {
      // 使用缓存的工具数据
      tools.splice(0, tools.length, ...cachedTools);
    } else {
      // 缓存当前工具数据
      cacheTools(tools);
    }
    
    // 初始化收藏状态
    const favorites = getCachedFavorites();
    tools.forEach(tool => {
      tool.isFavorite = favorites.includes(tool.id);
    });
  }, []);

  // 初始化引用
  useEffect(() => {
    tools.forEach(tool => {
      if (!toolRefs.current[tool.id]) {
        toolRefs.current[tool.id] = { ref: createRef() };
      }
    });
    
    sidebarMenu.forEach(section => {
      if (!categoryRefs.current[section.category]) {
        categoryRefs.current[section.category] = { ref: createRef() };
      }
    });
  }, []);

  // 记忆化的工具过滤结果
  const filteredToolsByCategory = useMemo(() => 
    getFilteredToolsByCategory(searchQuery), 
    [searchQuery]
  );

  // 记忆化的分类列表
  const categories = useMemo(() => 
    Object.keys(filteredToolsByCategory),
    [filteredToolsByCategory]
  );

  // 通知处理
  const addNotification = useCallback((notification: Notification) => {
    setNotifications(prev => [...prev, notification]);
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== notification.id));
    }, 3000);
  }, []);

  // 收藏处理
  const handleToggleFavorite = useCallback((toolId: string) => {
    const tool = toggleToolFavorite(toolId);
    if (tool) {
      updateCachedFavorites(toolId, tool.isFavorite);
      addNotification(
        createNotification(
          tool.isFavorite 
            ? (currentLocale === 'en' ? 'Added to Favorites' : '已添加到收藏夹')
            : (currentLocale === 'en' ? 'Removed from Favorites' : '已从收藏夹移除'),
          getLocalizedToolName(tool, currentLocale),
          'success'
        )
      );
    }
  }, [currentLocale, addNotification]);

  // 工具导航
  const navigateToTool = useCallback((toolId: string) => {
    if (['all', 'favorites', 'recent'].includes(toolId)) {
      if (toolId === 'all') {
        router.push(`/${currentLocale}/tools`);
      } else {
        router.push(`/${currentLocale}/tools?filter=${toolId}`);
      }
    } else {
      if (isToolDeveloped(toolId)) {
        updateCachedRecentTools(toolId);
        router.push(`/${currentLocale}/tools/${toolId}`);
      } else {
        const tool = tools.find(t => t.id === toolId);
        if (tool) {
          addNotification(
            createNotification(
              '功能提示',
              `${tool.name} 功能正在开发中`,
              'info'
            )
          );
        }
      }
    }
  }, [currentLocale, router, addNotification]);

  // 工具点击处理
  const handleToolClick = useCallback((toolId: string, event: React.MouseEvent) => {
    if (event.currentTarget.closest('#quick-nav-menu')) {
      const toolElement = toolRefs.current[toolId]?.ref?.current;
      if (toolElement) {
        toolElement.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center',
          inline: 'nearest'
        });
        
        setHighlightedTool(toolId);
        
        if (highlightTimeoutRef.current) {
          clearTimeout(highlightTimeoutRef.current);
        }
        
        highlightTimeoutRef.current = setTimeout(() => {
          setHighlightedTool(null);
        }, 3000);
        
        setIsQuickNavVisible(false);
        
        addNotification(
          createNotification(
            currentLocale === 'en' ? 'Tool Located' : '已定位到工具',
            currentLocale === 'en' 
              ? `Located to ${getLocalizedToolName(tools.find(t => t.id === toolId)!, currentLocale)}`
              : `已定位到 ${getLocalizedToolName(tools.find(t => t.id === toolId)!, currentLocale)}`,
            'info'
          )
        );
        
        return;
      }
    }
    
    if (!(event.target as HTMLElement).closest('.tool-actions')) {
      navigateToTool(toolId);
    }
  }, [currentLocale, navigateToTool, addNotification]);

  // 分类点击处理
  const handleCategoryClick = useCallback((category: string) => {
    setActiveCategory(category);
    const element = categoryRefs.current[category]?.ref.current;
    if (element && typeof window !== 'undefined') {
      const headerHeight = 80;
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.scrollY - headerHeight;
      
      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  }, []);

  // 清理
  useEffect(() => {
    return () => {
      if (highlightTimeoutRef.current) {
        clearTimeout(highlightTimeoutRef.current);
      }
    };
  }, []);

  // 挂载完成
  useEffect(() => {
    setMounted(true);
  }, []);

  // 快速导航提示
  useEffect(() => {
    if (mounted) {
      const timer = setTimeout(() => {
        addNotification(
          createNotification(
            '快速导航',
            '点击右下角按钮可快速跳转到任意工具类别',
            'info'
          )
        );
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [mounted, addNotification]);

  // 渲染工具卡片
  const renderToolCard = useCallback((tool: Tool, index: number) => (
    <ToolCard
      key={tool.id}
      tool={tool}
      index={index}
      isHighlighted={highlightedTool === tool.id}
      currentLocale={currentLocale}
      onToolClick={handleToolClick}
      onToggleFavorite={handleToggleFavorite}
    />
  ), [highlightedTool, currentLocale, handleToolClick, handleToggleFavorite]);

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1">
        <div className="animate-fade-in">
          {/* Hero Section with improved spacing and typography */}
          <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-accent/5">
            <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
            <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pt-20 pb-16 sm:pt-24 sm:pb-20">
              {/* 标题部分 - Enhanced with better typography and spacing */}
              <div className="text-center space-y-6">
                <div className="space-y-4">
                  <h1 className="text-4xl font-bold tracking-tight text-foreground sm:text-5xl lg:text-6xl">
                    <span className="block">
                      {currentLocale === 'en' ? 'All Tools' : '所有工具'}
                    </span>
                    <span className="block text-primary mt-2">
                      {currentLocale === 'en' ? 'In One Place' : '一站式平台'}
                    </span>
                  </h1>
                  <p className="mx-auto max-w-2xl text-lg text-muted-foreground sm:text-xl">
                    {currentLocale === 'en'
                      ? 'Discover and use powerful online tools to boost your productivity. From design and development to office and learning - find the perfect tool for every task.'
                      : '探索并使用强大的在线工具，提升您的工作效率。从设计开发到办公学习，为每项任务找到完美的工具。'}
                  </p>
                </div>

                {/* Quick Stats */}
                <div className="flex flex-wrap justify-center gap-8 pt-8">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">{tools.length}+</div>
                    <div className="text-sm text-muted-foreground">
                      {currentLocale === 'en' ? 'Tools Available' : '可用工具'}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">{categories.length}</div>
                    <div className="text-sm text-muted-foreground">
                      {currentLocale === 'en' ? 'Categories' : '分类'}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">100%</div>
                    <div className="text-sm text-muted-foreground">
                      {currentLocale === 'en' ? 'Free to Use' : '免费使用'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Enhanced Category Navigation */}
          <section className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-semibold text-foreground mb-2">
                {currentLocale === 'en' ? 'Browse by Category' : '按分类浏览'}
              </h2>
              <p className="text-muted-foreground">
                {currentLocale === 'en' ? 'Find tools organized by their purpose and functionality' : '按用途和功能查找工具'}
              </p>
            </div>

            <div className="bg-card border border-border rounded-2xl p-6 shadow-sm">
              <div className="flex flex-wrap items-center justify-center gap-3">
                <button
                  onClick={() => handleCategoryClick("all")}
                  className={`rounded-xl px-6 py-3 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                    activeCategory === "all" || !activeCategory
                      ? "bg-primary text-primary-foreground shadow-md scale-105"
                      : "bg-background hover:bg-accent hover:text-accent-foreground border border-border hover:border-primary/30 hover:shadow-sm"
                  }`}
                  aria-pressed={activeCategory === "all" || !activeCategory}
                  aria-label={currentLocale === 'en' ? 'Show all tools' : '显示所有工具'}
                >
                  {currentLocale === 'en' ? 'All Tools' : '全部工具'}
                </button>

                {categories.map((category) => {
                  const menuCategory = sidebarMenu.find(c => c.category === category);
                  const categoryName = menuCategory
                    ? getLocalizedCategoryName(menuCategory, currentLocale)
                    : category;

                  const toolCount = filteredToolsByCategory[category]?.length || 0;

                  return (
                    <button
                      key={category}
                      onClick={() => handleCategoryClick(category)}
                      className={`group rounded-xl px-6 py-3 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                        activeCategory === category
                          ? "bg-primary text-primary-foreground shadow-md scale-105"
                          : "bg-background hover:bg-accent hover:text-accent-foreground border border-border hover:border-primary/30 hover:shadow-sm"
                      }`}
                      aria-pressed={activeCategory === category}
                      aria-label={`${currentLocale === 'en' ? 'Show' : '显示'} ${categoryName} ${currentLocale === 'en' ? 'tools' : '工具'}`}
                    >
                      <span className="flex items-center gap-2">
                        {categoryName}
                        <span className={`text-xs px-2 py-0.5 rounded-full ${
                          activeCategory === category
                            ? "bg-primary-foreground/20 text-primary-foreground"
                            : "bg-muted text-muted-foreground group-hover:bg-accent-foreground/20"
                        }`}>
                          {toolCount}
                        </span>
                      </span>
                    </button>
                  );
                })}
              </div>
            </div>
          </section>
            
          {/* Enhanced Tools Grid Section */}
          <section className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
            <AnimatePresence mode="wait">
              {Object.keys(filteredToolsByCategory).length === 0 ? (
                <motion.div
                  key="empty"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="text-center py-20"
                >
                  <div className="mx-auto max-w-md">
                    <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted flex items-center justify-center">
                      <Search className="w-8 h-8 text-muted-foreground" />
                    </div>
                    <h3 className="text-xl font-semibold text-foreground mb-2">
                      {currentLocale === 'en' ? 'No tools found' : '未找到工具'}
                    </h3>
                    <p className="text-muted-foreground">
                      {currentLocale === 'en'
                        ? 'Try using different keywords or select another category'
                        : '尝试使用不同的关键词或选择其他分类'}
                    </p>
                  </div>
                </motion.div>
              ) : (
                <motion.div
                  key="content"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  {!activeCategory || activeCategory === "all" ? (
                    <div className="space-y-16">
                      {Object.entries(filteredToolsByCategory).map(([category, categoryTools]) => {
                        const menuCategory = sidebarMenu.find(c => c.category === category);
                        const categoryName = menuCategory
                          ? getLocalizedCategoryName(menuCategory, currentLocale)
                          : category;

                        return (
                          <div key={category} ref={categoryRefs.current[category]?.ref} className="scroll-mt-32">
                            <div className="flex items-center justify-between mb-8">
                              <div>
                                <h2 className="text-2xl font-bold text-foreground">{categoryName}</h2>
                                <p className="text-muted-foreground mt-1">
                                  {categoryTools.length} {currentLocale === 'en' ? 'tools available' : '个工具可用'}
                                </p>
                              </div>
                              <button
                                onClick={() => handleCategoryClick(category)}
                                className="text-sm text-primary hover:text-primary/80 font-medium"
                              >
                                {currentLocale === 'en' ? 'View all' : '查看全部'} →
                              </button>
                            </div>
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                              {categoryTools.map((tool, index) => renderToolCard(tool, index))}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="space-y-8">
                      {activeCategory && filteredToolsByCategory[activeCategory] && (
                        <div ref={categoryRefs.current[activeCategory]?.ref} className="scroll-mt-32">
                          <div className="mb-8">
                            <h2 className="text-2xl font-bold text-foreground mb-2">
                              {(() => {
                                const menuCategory = sidebarMenu.find(c => c.category === activeCategory);
                                return menuCategory
                                  ? getLocalizedCategoryName(menuCategory, currentLocale)
                                  : activeCategory;
                              })()}
                            </h2>
                            <p className="text-muted-foreground">
                              {filteredToolsByCategory[activeCategory].length} {currentLocale === 'en' ? 'tools in this category' : '个工具在此分类中'}
                            </p>
                          </div>
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                            {filteredToolsByCategory[activeCategory].map((tool, index) => renderToolCard(tool, index))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </section>
        </div>
      </main>

      {/* 快速导航按钮 */}
      {mounted && (
        <QuickNavMenu
          isVisible={isQuickNavVisible}
          onToggle={() => setIsQuickNavVisible(!isQuickNavVisible)}
          categories={categories}
          tools={tools}
          currentLocale={currentLocale}
          filteredToolsByCategory={filteredToolsByCategory}
          onCategoryClick={handleCategoryClick}
          onToolClick={handleToolClick}
          hoveredCategory={hoveredCategory}
          setHoveredCategory={setHoveredCategory}
        />
      )}

      {/* 通知区域 */}
      <AnimatePresence>
        {notifications.map((notification) => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className={`fixed bottom-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
              notification.type === 'success' ? 'bg-green-500' :
              notification.type === 'error' ? 'bg-red-500' :
              'bg-blue-500'
            } text-white dark:text-white`}
          >
            <h4 className="font-medium">{notification.title}</h4>
            <p className="text-sm">{notification.message}</p>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}
