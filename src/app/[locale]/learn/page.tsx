'use client';

import { useState } from 'react';
import { ArrowLeft, BookOpen, Clock, Users, Star, ChevronRight, Search, Filter } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';

// 学习资源数据
const learningResources = [
  {
    id: 'web-development-fundamentals',
    title: {
      zh: 'Web开发基础教程',
      en: 'Web Development Fundamentals'
    },
    description: {
      zh: '从零开始学习Web开发，包括HTML、CSS、JavaScript基础知识和最佳实践',
      en: 'Learn web development from scratch, including HTML, CSS, JavaScript fundamentals and best practices'
    },
    level: 'beginner',
    duration: '8小时',
    lessons: 24,
    category: 'development',
    rating: 4.8,
    students: 1250,
    imageUrl: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6',
    featured: true
  },
  {
    id: 'advanced-seo-mastery',
    title: {
      zh: 'SEO高级优化大师课',
      en: 'Advanced SEO Mastery Course'
    },
    description: {
      zh: '深入学习搜索引擎优化的高级技术，包括技术SEO、内容策略、链接建设等',
      en: 'Deep dive into advanced search engine optimization techniques, including technical SEO, content strategy, and link building'
    },
    level: 'advanced',
    duration: '12小时',
    lessons: 36,
    category: 'seo',
    rating: 4.9,
    students: 890,
    imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f',
    featured: true
  },
  {
    id: 'cybersecurity-essentials',
    title: {
      zh: '网络安全基础知识',
      en: 'Cybersecurity Essentials'
    },
    description: {
      zh: '了解网络安全的基本概念、常见威胁和防护措施，保护您的数字资产',
      en: 'Understand basic cybersecurity concepts, common threats, and protection measures to secure your digital assets'
    },
    level: 'intermediate',
    duration: '10小时',
    lessons: 30,
    category: 'security',
    rating: 4.7,
    students: 1100,
    imageUrl: 'https://images.unsplash.com/photo-1563013544-824ae1b704d3',
    featured: false
  },
  {
    id: 'domain-management-guide',
    title: {
      zh: '域名管理完全指南',
      en: 'Complete Domain Management Guide'
    },
    description: {
      zh: '学习域名注册、管理、安全防护和优化的全套知识和技能',
      en: 'Learn comprehensive knowledge and skills for domain registration, management, security, and optimization'
    },
    level: 'intermediate',
    duration: '6小时',
    lessons: 18,
    category: 'domains',
    rating: 4.6,
    students: 750,
    imageUrl: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d',
    featured: false
  },
  {
    id: 'api-development-best-practices',
    title: {
      zh: 'API开发最佳实践',
      en: 'API Development Best Practices'
    },
    description: {
      zh: '掌握RESTful API设计原则、安全性、文档编写和测试方法',
      en: 'Master RESTful API design principles, security, documentation, and testing methods'
    },
    level: 'advanced',
    duration: '14小时',
    lessons: 42,
    category: 'development',
    rating: 4.8,
    students: 650,
    imageUrl: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31',
    featured: false
  },
  {
    id: 'data-formats-mastery',
    title: {
      zh: '数据格式精通课程',
      en: 'Data Formats Mastery Course'
    },
    description: {
      zh: '深入了解JSON、YAML、XML等数据格式的特点、应用和转换技巧',
      en: 'Deep understanding of characteristics, applications, and conversion techniques for JSON, YAML, XML, and other data formats'
    },
    level: 'intermediate',
    duration: '8小时',
    lessons: 24,
    category: 'development',
    rating: 4.5,
    students: 520,
    imageUrl: 'https://images.unsplash.com/photo-1551808525-51a94da548ce',
    featured: false
  }
];

// 分类和级别定义
const categories = [
  { id: 'all', name: { zh: '全部', en: 'All' } },
  { id: 'development', name: { zh: '开发', en: 'Development' } },
  { id: 'seo', name: { zh: 'SEO', en: 'SEO' } },
  { id: 'security', name: { zh: '安全', en: 'Security' } },
  { id: 'domains', name: { zh: '域名', en: 'Domains' } }
];

const levels = [
  { id: 'all', name: { zh: '全部级别', en: 'All Levels' } },
  { id: 'beginner', name: { zh: '初级', en: 'Beginner' } },
  { id: 'intermediate', name: { zh: '中级', en: 'Intermediate' } },
  { id: 'advanced', name: { zh: '高级', en: 'Advanced' } }
];

export default function LearnPage() {
  const router = useRouter();
  const pathname = usePathname();
  const locale = pathname.split('/')[1] as 'zh' | 'en';
  
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [activeLevel, setActiveLevel] = useState('all');

  // 过滤学习资源
  const filteredResources = learningResources.filter(resource => {
    const matchesSearch = resource.title[locale].toLowerCase().includes(searchQuery.toLowerCase()) ||
                         resource.description[locale].toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = activeCategory === 'all' || resource.category === activeCategory;
    const matchesLevel = activeLevel === 'all' || resource.level === activeLevel;
    return matchesSearch && matchesCategory && matchesLevel;
  });

  const featuredResources = learningResources.filter(resource => resource.featured);

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getLevelText = (level: string) => {
    const levelMap = {
      beginner: { zh: '初级', en: 'Beginner' },
      intermediate: { zh: '中级', en: 'Intermediate' },
      advanced: { zh: '高级', en: 'Advanced' }
    };
    return levelMap[level as keyof typeof levelMap]?.[locale] || level;
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto">
        {/* 导航 */}
        <nav aria-label="返回导航">
          <button 
            onClick={() => router.back()}
            className="mb-6 flex items-center gap-2 text-primary hover:underline"
            aria-label="返回首页"
          >
            <ArrowLeft size={16} />
            <span>{locale === 'zh' ? '返回首页' : 'Back to Home'}</span>
          </button>
        </nav>

        {/* 页面标题 */}
        <header className="mb-10 text-center">
          <h1 className="text-4xl font-bold mb-4">
            {locale === 'zh' ? '学习中心' : 'Learning Center'}
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            {locale === 'zh' 
              ? '通过我们的专业课程和教程，掌握Web开发、SEO优化、网络安全等核心技能，提升您的技术水平和职业竞争力'
              : 'Master core skills in web development, SEO optimization, cybersecurity, and more through our professional courses and tutorials to enhance your technical expertise and career competitiveness'
            }
          </p>
        </header>

        {/* 搜索和筛选 */}
        <div className="mb-10 space-y-6">
          {/* 搜索框 */}
          <div className="relative max-w-2xl mx-auto">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={18} className="text-muted-foreground" />
            </div>
            <input
              type="text"
              placeholder={locale === 'zh' ? '搜索课程和教程...' : 'Search courses and tutorials...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>

          {/* 分类和级别筛选 */}
          <div className="flex flex-wrap items-center justify-center gap-4">
            <div className="flex items-center gap-2">
              <Filter size={16} className="text-muted-foreground" />
              <span className="text-sm font-medium">
                {locale === 'zh' ? '分类：' : 'Category:'}
              </span>
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`px-3 py-1 text-sm rounded-full transition-all ${
                      activeCategory === category.id
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-accent hover:bg-accent/80'
                    }`}
                  >
                    {category.name[locale]}
                  </button>
                ))}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">
                {locale === 'zh' ? '级别：' : 'Level:'}
              </span>
              <div className="flex flex-wrap gap-2">
                {levels.map((level) => (
                  <button
                    key={level.id}
                    onClick={() => setActiveLevel(level.id)}
                    className={`px-3 py-1 text-sm rounded-full transition-all ${
                      activeLevel === level.id
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-accent hover:bg-accent/80'
                    }`}
                  >
                    {level.name[locale]}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 特色课程 */}
        {activeCategory === 'all' && activeLevel === 'all' && !searchQuery && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6">
              {locale === 'zh' ? '特色课程' : 'Featured Courses'}
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              {featuredResources.map((resource) => (
                <div key={resource.id} className="bg-card border border-border rounded-xl overflow-hidden hover:shadow-lg transition-shadow">
                  <div 
                    className="h-48 bg-cover bg-center" 
                    style={{ backgroundImage: `url(${resource.imageUrl})` }}
                  ></div>
                  <div className="p-6">
                    <div className="flex items-center gap-2 mb-3">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getLevelColor(resource.level)}`}>
                        {getLevelText(resource.level)}
                      </span>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Star size={14} className="mr-1 fill-yellow-400 text-yellow-400" />
                        <span>{resource.rating}</span>
                      </div>
                    </div>
                    <h3 className="text-xl font-semibold mb-2">{resource.title[locale]}</h3>
                    <p className="text-muted-foreground mb-4">{resource.description[locale]}</p>
                    <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                      <div className="flex items-center">
                        <Clock size={14} className="mr-1" />
                        <span>{resource.duration}</span>
                      </div>
                      <div className="flex items-center">
                        <BookOpen size={14} className="mr-1" />
                        <span>{resource.lessons} {locale === 'zh' ? '课时' : 'lessons'}</span>
                      </div>
                      <div className="flex items-center">
                        <Users size={14} className="mr-1" />
                        <span>{resource.students} {locale === 'zh' ? '学员' : 'students'}</span>
                      </div>
                    </div>
                    <Link 
                      href={`/${locale}/learn/${resource.id}`}
                      className="inline-flex items-center text-primary hover:underline"
                    >
                      {locale === 'zh' ? '开始学习' : 'Start Learning'} <ChevronRight size={16} className="ml-1" />
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 所有课程 */}
        <div>
          <h2 className="text-2xl font-bold mb-6">
            {locale === 'zh' ? '所有课程' : 'All Courses'}
          </h2>
          {filteredResources.length > 0 ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredResources.map((resource) => (
                <div key={resource.id} className="bg-card border border-border rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                  <div 
                    className="h-40 bg-cover bg-center" 
                    style={{ backgroundImage: `url(${resource.imageUrl})` }}
                  ></div>
                  <div className="p-5">
                    <div className="flex items-center gap-2 mb-2">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getLevelColor(resource.level)}`}>
                        {getLevelText(resource.level)}
                      </span>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Star size={12} className="mr-1 fill-yellow-400 text-yellow-400" />
                        <span>{resource.rating}</span>
                      </div>
                    </div>
                    <h3 className="text-lg font-semibold mb-2 line-clamp-2">{resource.title[locale]}</h3>
                    <p className="text-sm text-muted-foreground mb-3 line-clamp-2">{resource.description[locale]}</p>
                    <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
                      <span>{resource.duration}</span>
                      <span>{resource.lessons} {locale === 'zh' ? '课时' : 'lessons'}</span>
                      <span>{resource.students} {locale === 'zh' ? '学员' : 'students'}</span>
                    </div>
                    <Link 
                      href={`/${locale}/learn/${resource.id}`}
                      className="inline-flex items-center text-primary text-sm hover:underline"
                    >
                      {locale === 'zh' ? '开始学习' : 'Start Learning'} <ChevronRight size={14} className="ml-1" />
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-medium">
                {locale === 'zh' ? '没有找到相关课程' : 'No courses found'}
              </h3>
              <p className="text-muted-foreground mt-2 mb-4">
                {locale === 'zh' ? '尝试调整搜索条件或筛选选项' : 'Try adjusting your search terms or filter options'}
              </p>
              <button
                onClick={() => {
                  setSearchQuery('');
                  setActiveCategory('all');
                  setActiveLevel('all');
                }}
                className="text-primary hover:underline"
              >
                {locale === 'zh' ? '重置筛选条件' : 'Reset filters'}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
