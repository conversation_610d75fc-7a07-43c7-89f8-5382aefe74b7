'use client';

import { useState } from 'react';
import { ArrowLeft, Search, BookOpen, ExternalLink } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';

// 术语词汇数据
const glossaryTerms = [
  {
    id: 'api',
    term: 'API',
    fullName: {
      zh: '应用程序编程接口',
      en: 'Application Programming Interface'
    },
    definition: {
      zh: 'API是一组定义和协议，用于构建和集成应用软件。它定义了不同软件组件之间如何相互通信的规则和方法。',
      en: 'API is a set of definitions and protocols for building and integrating application software. It defines the rules and methods for how different software components communicate with each other.'
    },
    category: 'development',
    relatedTerms: ['REST', 'HTTP', 'JSON', 'SDK'],
    examples: {
      zh: '例如：Twitter API允许开发者访问推文数据，Google Maps API提供地图服务功能。',
      en: 'Examples: Twitter API allows developers to access tweet data, Google Maps API provides mapping functionality.'
    }
  },
  {
    id: 'dns',
    term: 'DNS',
    fullName: {
      zh: '域名系统',
      en: 'Domain Name System'
    },
    definition: {
      zh: 'DNS是互联网的"电话簿"，将人类可读的域名（如google.com）转换为计算机可理解的IP地址（如***********）。',
      en: 'DNS is the "phonebook" of the internet, translating human-readable domain names (like google.com) into computer-understandable IP addresses (like ***********).'
    },
    category: 'networking',
    relatedTerms: ['Domain', 'IP Address', 'WHOIS', 'TLD'],
    examples: {
      zh: '当您在浏览器中输入www.example.com时，DNS服务器会查找对应的IP地址并建立连接。',
      en: 'When you type www.example.com in your browser, DNS servers look up the corresponding IP address and establish the connection.'
    }
  },
  {
    id: 'seo',
    term: 'SEO',
    fullName: {
      zh: '搜索引擎优化',
      en: 'Search Engine Optimization'
    },
    definition: {
      zh: 'SEO是通过优化网站内容、结构和技术要素来提高网站在搜索引擎结果页面中排名的实践。',
      en: 'SEO is the practice of improving website rankings in search engine results pages by optimizing content, structure, and technical elements.'
    },
    category: 'marketing',
    relatedTerms: ['Keywords', 'Meta Tags', 'Backlinks', 'SERP'],
    examples: {
      zh: '通过优化标题标签、创建高质量内容和建立外部链接来提高Google搜索排名。',
      en: 'Improving Google search rankings by optimizing title tags, creating high-quality content, and building external links.'
    }
  },
  {
    id: 'ssl',
    term: 'SSL',
    fullName: {
      zh: '安全套接字层',
      en: 'Secure Sockets Layer'
    },
    definition: {
      zh: 'SSL是一种安全协议，用于在客户端和服务器之间建立加密连接，保护数据传输的安全性。现已被TLS取代。',
      en: 'SSL is a security protocol used to establish encrypted connections between clients and servers, protecting data transmission security. Now replaced by TLS.'
    },
    category: 'security',
    relatedTerms: ['TLS', 'HTTPS', 'Certificate', 'Encryption'],
    examples: {
      zh: '网站使用SSL证书后，浏览器地址栏会显示锁形图标，URL以https://开头。',
      en: 'When a website uses an SSL certificate, browsers display a lock icon in the address bar and the URL starts with https://.'
    }
  },
  {
    id: 'json',
    term: 'JSON',
    fullName: {
      zh: 'JavaScript对象表示法',
      en: 'JavaScript Object Notation'
    },
    definition: {
      zh: 'JSON是一种轻量级的数据交换格式，易于人类阅读和编写，同时也易于机器解析和生成。',
      en: 'JSON is a lightweight data interchange format that is easy for humans to read and write, and easy for machines to parse and generate.'
    },
    category: 'development',
    relatedTerms: ['XML', 'YAML', 'API', 'JavaScript'],
    examples: {
      zh: '{"name": "张三", "age": 30, "city": "北京"}',
      en: '{"name": "John", "age": 30, "city": "New York"}'
    }
  },
  {
    id: 'whois',
    term: 'WHOIS',
    fullName: {
      zh: 'WHOIS查询',
      en: 'WHOIS Lookup'
    },
    definition: {
      zh: 'WHOIS是一个查询和响应协议，用于查询域名、IP地址和自治系统的注册信息。',
      en: 'WHOIS is a query and response protocol used to look up registration information for domain names, IP addresses, and autonomous systems.'
    },
    category: 'networking',
    relatedTerms: ['Domain', 'Registrar', 'DNS', 'IP Address'],
    examples: {
      zh: '通过WHOIS查询可以了解域名的注册时间、到期时间、注册商等信息。',
      en: 'WHOIS queries can reveal domain registration date, expiration date, registrar, and other information.'
    }
  },
  {
    id: 'cdn',
    term: 'CDN',
    fullName: {
      zh: '内容分发网络',
      en: 'Content Delivery Network'
    },
    definition: {
      zh: 'CDN是一个分布式服务器网络，通过将内容缓存到离用户更近的服务器上来加速网站加载速度。',
      en: 'CDN is a distributed network of servers that speeds up website loading by caching content on servers closer to users.'
    },
    category: 'networking',
    relatedTerms: ['Cache', 'Latency', 'Performance', 'Edge Server'],
    examples: {
      zh: 'Cloudflare、AWS CloudFront等CDN服务可以显著提高网站访问速度。',
      en: 'CDN services like Cloudflare and AWS CloudFront can significantly improve website access speed.'
    }
  },
  {
    id: 'regex',
    term: 'Regex',
    fullName: {
      zh: '正则表达式',
      en: 'Regular Expression'
    },
    definition: {
      zh: '正则表达式是一种用于匹配字符串模式的强大工具，广泛用于文本搜索、验证和替换操作。',
      en: 'Regular expressions are powerful tools for matching string patterns, widely used for text searching, validation, and replacement operations.'
    },
    category: 'development',
    relatedTerms: ['Pattern Matching', 'String', 'Validation', 'Programming'],
    examples: {
      zh: '/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/ 用于验证邮箱格式',
      en: '/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/ for email validation'
    }
  }
];

// 分类定义
const categories = [
  { id: 'all', name: { zh: '全部', en: 'All' } },
  { id: 'development', name: { zh: '开发', en: 'Development' } },
  { id: 'networking', name: { zh: '网络', en: 'Networking' } },
  { id: 'security', name: { zh: '安全', en: 'Security' } },
  { id: 'marketing', name: { zh: '营销', en: 'Marketing' } }
];

export default function GlossaryPage() {
  const router = useRouter();
  const pathname = usePathname();
  const locale = pathname.split('/')[1] as 'zh' | 'en';
  
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [expandedTerm, setExpandedTerm] = useState<string | null>(null);

  // 过滤术语
  const filteredTerms = glossaryTerms.filter(term => {
    const matchesSearch = term.term.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         term.fullName[locale].toLowerCase().includes(searchQuery.toLowerCase()) ||
                         term.definition[locale].toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = activeCategory === 'all' || term.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  // 按字母排序
  const sortedTerms = filteredTerms.sort((a, b) => a.term.localeCompare(b.term));

  const toggleTerm = (termId: string) => {
    setExpandedTerm(expandedTerm === termId ? null : termId);
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-5xl mx-auto">
        {/* 导航 */}
        <nav aria-label="返回导航">
          <button 
            onClick={() => router.back()}
            className="mb-6 flex items-center gap-2 text-primary hover:underline"
            aria-label="返回首页"
          >
            <ArrowLeft size={16} />
            <span>{locale === 'zh' ? '返回首页' : 'Back to Home'}</span>
          </button>
        </nav>

        {/* 页面标题 */}
        <header className="mb-10 text-center">
          <h1 className="text-3xl font-bold mb-4">
            {locale === 'zh' ? '技术词汇表' : 'Technical Glossary'}
          </h1>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            {locale === 'zh' 
              ? '查找和了解Web开发、网络技术、安全和数字营销领域的专业术语和概念'
              : 'Find and understand professional terms and concepts in web development, networking, security, and digital marketing'
            }
          </p>
        </header>

        {/* 搜索和筛选 */}
        <div className="mb-10 space-y-6">
          {/* 搜索框 */}
          <div className="relative max-w-2xl mx-auto">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={18} className="text-muted-foreground" />
            </div>
            <input
              type="text"
              placeholder={locale === 'zh' ? '搜索术语或定义...' : 'Search terms or definitions...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>

          {/* 分类筛选 */}
          <div className="flex flex-wrap items-center justify-center gap-3">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`px-4 py-2 text-sm rounded-full transition-all ${
                  activeCategory === category.id
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-accent hover:bg-accent/80'
                }`}
              >
                {category.name[locale]}
              </button>
            ))}
          </div>
        </div>

        {/* 术语列表 */}
        <div className="space-y-4">
          {sortedTerms.length > 0 ? (
            sortedTerms.map((term) => (
              <div key={term.id} className="bg-card border border-border rounded-lg">
                <button
                  onClick={() => toggleTerm(term.id)}
                  className="w-full flex items-center justify-between px-6 py-4 text-left hover:bg-accent/50 transition-colors"
                >
                  <div>
                    <h3 className="text-lg font-semibold">{term.term}</h3>
                    <p className="text-sm text-muted-foreground">{term.fullName[locale]}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="px-2 py-1 text-xs bg-accent rounded-full">
                      {categories.find(c => c.id === term.category)?.name[locale]}
                    </span>
                    <BookOpen size={16} className="text-muted-foreground" />
                  </div>
                </button>
                
                {expandedTerm === term.id && (
                  <div className="px-6 pb-6 border-t border-border">
                    <div className="pt-4 space-y-4">
                      <div>
                        <h4 className="font-medium mb-2">
                          {locale === 'zh' ? '定义' : 'Definition'}
                        </h4>
                        <p className="text-muted-foreground">{term.definition[locale]}</p>
                      </div>
                      
                      {term.examples && (
                        <div>
                          <h4 className="font-medium mb-2">
                            {locale === 'zh' ? '示例' : 'Examples'}
                          </h4>
                          <p className="text-muted-foreground text-sm bg-accent/30 p-3 rounded">
                            {term.examples[locale]}
                          </p>
                        </div>
                      )}
                      
                      {term.relatedTerms && term.relatedTerms.length > 0 && (
                        <div>
                          <h4 className="font-medium mb-2">
                            {locale === 'zh' ? '相关术语' : 'Related Terms'}
                          </h4>
                          <div className="flex flex-wrap gap-2">
                            {term.relatedTerms.map((relatedTerm) => (
                              <span
                                key={relatedTerm}
                                className="px-2 py-1 text-xs bg-primary/10 text-primary rounded cursor-pointer hover:bg-primary/20 transition-colors"
                                onClick={() => {
                                  setSearchQuery(relatedTerm);
                                  setExpandedTerm(null);
                                }}
                              >
                                {relatedTerm}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-medium">
                {locale === 'zh' ? '没有找到相关术语' : 'No terms found'}
              </h3>
              <p className="text-muted-foreground mt-2 mb-4">
                {locale === 'zh' ? '尝试使用不同的搜索词或选择其他分类' : 'Try using different search terms or select another category'}
              </p>
              <button
                onClick={() => {
                  setSearchQuery('');
                  setActiveCategory('all');
                }}
                className="text-primary hover:underline"
              >
                {locale === 'zh' ? '重置搜索' : 'Reset search'}
              </button>
            </div>
          )}
        </div>

        {/* 底部提示 */}
        <div className="mt-12 text-center">
          <div className="bg-card border border-border rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-2">
              {locale === 'zh' ? '没有找到您需要的术语？' : 'Can\'t find the term you need?'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {locale === 'zh' 
                ? '我们持续更新词汇表，如果您有建议或需要添加新术语，请联系我们'
                : 'We continuously update our glossary. If you have suggestions or need new terms added, please contact us'
              }
            </p>
            <Link 
              href={`/${locale}/contact`}
              className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              {locale === 'zh' ? '联系我们' : 'Contact Us'}
              <ExternalLink size={16} className="ml-2" />
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
