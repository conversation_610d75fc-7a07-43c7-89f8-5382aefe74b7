import { redirect } from 'next/navigation';
import { defaultLocale } from '@/i18n/routing';

/**
 * Global not-found page for the root app directory.
 * This handles 404 errors that occur outside of the locale-specific routes.
 * It redirects to the default locale's not-found page to ensure proper internationalization.
 */
export default function GlobalNotFound() {
  // Redirect to the default locale's not-found page
  // This ensures that users always see a properly internationalized 404 page
  redirect(`/${defaultLocale}/not-found`);
}
