import { Metada<PERSON> } from 'next';
import { SEOMetadata, OpenGraphData, TwitterCardData } from './types';
import { seoConfig, localizedSEOConfig } from './config';

/**
 * Generate comprehensive metadata for pages
 */
export function generateSEOMetadata(
  seoData: Partial<SEOMetadata>,
  locale: string = 'zh'
): Metadata {
  const config = localizedSEOConfig[locale as keyof typeof localizedSEOConfig] || localizedSEOConfig.zh;
  
  const title = seoData.title || config.defaultTitle;
  const description = seoData.description || config.defaultDescription;
  const keywords = seoData.keywords || config.defaultKeywords;
  
  const metadata: Metadata = {
    title,
    description,
    keywords,
    metadataBase: new URL(seoConfig.baseUrl),
    alternates: {
      canonical: seoData.canonical || '/',
      languages: seoData.alternates?.languages || {
        'zh': '/zh',
        'en': '/en',
      },
    },
    openGraph: generateOpenGraphData({
      title,
      description,
      url: seoData.canonical || seoConfig.baseUrl,
      siteName: config.siteName,
      locale,
      type: 'website',
      ...seoData.openGraph,
    }),
    twitter: generateTwitterCardData({
      card: 'summary_large_image',
      title,
      description,
      ...seoData.twitter,
    }),
    robots: {
      index: seoData.robots?.index ?? true,
      follow: seoData.robots?.follow ?? true,
      noarchive: seoData.robots?.noarchive ?? false,
      nosnippet: seoData.robots?.nosnippet ?? false,
      noimageindex: seoData.robots?.noimageindex ?? false,
    },
    verification: {
      google: seoData.verification?.google || seoConfig.googleSiteVerification,
      other: {
        'msvalidate.01': seoData.verification?.bing || seoConfig.bingSiteVerification || '',
        'yandex-verification': seoData.verification?.yandex || seoConfig.yandexSiteVerification || '',
      },
    },
    viewport: {
      width: 'device-width',
      initialScale: 1,
      maximumScale: 1,
    },
    category: 'technology',
  };

  return metadata;
}

/**
 * Generate Open Graph metadata
 */
export function generateOpenGraphData(ogData: OpenGraphData): Metadata['openGraph'] {
  // Filter out 'product' type as it's not supported by Next.js Metadata
  const validType = ogData.type === 'product' ? 'website' : (ogData.type || 'website');

  return {
    title: ogData.title,
    description: ogData.description,
    url: ogData.url,
    siteName: ogData.siteName,
    locale: ogData.locale,
    type: validType as 'website' | 'article' | 'profile',
    publishedTime: ogData.publishedTime,
    modifiedTime: ogData.modifiedTime,
    authors: ogData.authors,
    section: ogData.section,
    tags: ogData.tags,
    images: ogData.images?.map(img => ({
      url: img.url,
      width: img.width,
      height: img.height,
      alt: img.alt,
      type: img.type,
    })) || [{
      url: `${seoConfig.baseUrl}/images/og-default.jpg`,
      width: 1200,
      height: 630,
      alt: ogData.title,
    }],
  };
}

/**
 * Generate Twitter Card metadata
 */
export function generateTwitterCardData(twitterData: TwitterCardData): Metadata['twitter'] {
  return {
    card: twitterData.card,
    title: twitterData.title,
    description: twitterData.description,
    creator: twitterData.creator || seoConfig.twitterHandle,
    site: twitterData.site || seoConfig.twitterHandle,
    images: twitterData.images || [`${seoConfig.baseUrl}/images/twitter-default.jpg`],
  };
}

/**
 * Generate tool-specific metadata
 */
export function generateToolMetadata(
  toolId: string,
  toolName: string,
  toolDescription: string,
  locale: string = 'zh'
): Metadata {
  const config = localizedSEOConfig[locale as keyof typeof localizedSEOConfig] || localizedSEOConfig.zh;
  
  const title = locale === 'zh' 
    ? `${toolName} | ${config.siteName} - 免费在线工具`
    : `${toolName} | ${config.siteName} - Free Online Tool`;
    
  const description = toolDescription;
  
  const keywords = locale === 'zh'
    ? `${toolName}, 在线工具, 免费工具, ${toolId}, ${config.siteName}`
    : `${toolName}, online tool, free tool, ${toolId}, ${config.siteName}`;

  return generateSEOMetadata({
    title,
    description,
    keywords,
    canonical: `/${locale}/tools/${toolId}`,
    openGraph: {
      title,
      description,
      url: `${seoConfig.baseUrl}/${locale}/tools/${toolId}`,
      siteName: config.siteName,
      type: 'website',
      images: [{
        url: `${seoConfig.baseUrl}/images/tools/${toolId}-og.jpg`,
        width: 1200,
        height: 630,
        alt: toolName,
      }],
    },
  }, locale);
}

/**
 * Generate blog post metadata
 */
export function generateBlogMetadata(
  slug: string,
  title: string,
  description: string,
  publishedTime: string,
  modifiedTime?: string,
  author?: string,
  tags?: string[],
  locale: string = 'zh'
): Metadata {
  const config = localizedSEOConfig[locale as keyof typeof localizedSEOConfig] || localizedSEOConfig.zh;
  
  const fullTitle = `${title} | ${config.siteName}`;
  
  return generateSEOMetadata({
    title: fullTitle,
    description,
    canonical: `/${locale}/blog/${slug}`,
    openGraph: {
      title: fullTitle,
      description,
      url: `${seoConfig.baseUrl}/${locale}/blog/${slug}`,
      siteName: config.siteName,
      type: 'article',
      publishedTime,
      modifiedTime,
      authors: author ? [author] : undefined,
      tags,
      images: [{
        url: `${seoConfig.baseUrl}/images/blog/${slug}-og.jpg`,
        width: 1200,
        height: 630,
        alt: title,
      }],
    },
  }, locale);
}

/**
 * Generate category page metadata
 */
export function generateCategoryMetadata(
  category: string,
  categoryName: string,
  description: string,
  locale: string = 'zh'
): Metadata {
  const config = localizedSEOConfig[locale as keyof typeof localizedSEOConfig] || localizedSEOConfig.zh;
  
  const title = locale === 'zh'
    ? `${categoryName} | ${config.siteName} - 在线工具分类`
    : `${categoryName} | ${config.siteName} - Online Tools Category`;
    
  return generateSEOMetadata({
    title,
    description,
    canonical: `/${locale}/tools/category/${category}`,
    openGraph: {
      title,
      description,
      url: `${seoConfig.baseUrl}/${locale}/tools/category/${category}`,
      siteName: config.siteName,
      type: 'website',
    },
  }, locale);
}

/**
 * Validate metadata for SEO best practices
 */
export function validateMetadata(metadata: SEOMetadata): string[] {
  const issues: string[] = [];
  
  // Title validation
  if (!metadata.title) {
    issues.push('Title is missing');
  } else {
    if (metadata.title.length < 30) {
      issues.push('Title is too short (minimum 30 characters)');
    }
    if (metadata.title.length > 60) {
      issues.push('Title is too long (maximum 60 characters)');
    }
  }
  
  // Description validation
  if (!metadata.description) {
    issues.push('Description is missing');
  } else {
    if (metadata.description.length < 120) {
      issues.push('Description is too short (minimum 120 characters)');
    }
    if (metadata.description.length > 160) {
      issues.push('Description is too long (maximum 160 characters)');
    }
  }
  
  // Keywords validation
  if (metadata.keywords) {
    const keywordCount = metadata.keywords.split(',').length;
    if (keywordCount > 10) {
      issues.push('Too many keywords (maximum 10 recommended)');
    }
  }
  
  // Open Graph validation
  if (metadata.openGraph) {
    if (!metadata.openGraph.images || metadata.openGraph.images.length === 0) {
      issues.push('Open Graph image is missing');
    }
  }
  
  return issues;
}
