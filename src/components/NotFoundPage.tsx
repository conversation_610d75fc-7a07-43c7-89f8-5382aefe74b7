"use client";

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  Home, 
  Search, 
  ArrowLeft, 
  Grid3X3, 
  Mail, 
  ExternalLink,
  AlertTriangle,
  Compass
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from '@/i18n/routing';
import { tools, Tool } from '@/lib/tools';

interface NotFoundPageProps {
  locale: string;
}

export default function NotFoundPage({ locale }: NotFoundPageProps) {
  const t = useTranslations('notFound');
  const router = useRouter();
  const pathname = usePathname();
  const [searchQuery, setSearchQuery] = useState('');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Get popular tools (first 6 tools for display)
  const popularTools = tools.slice(0, 6);

  // Get tool categories
  const categories = [
    { id: 'commonTools', name: t('categories.title'), icon: '🔧' },
    { id: 'jsonTools', name: 'JSON Tools', icon: '📄' },
    { id: 'securityTools', name: 'Security Tools', icon: '🔒' },
    { id: 'domainIp', name: 'Domain/IP', icon: '🌐' },
    { id: 'imageProcessing', name: 'Image Processing', icon: '🖼️' },
    { id: 'textProcessing', name: 'Text Processing', icon: '📝' }
  ];

  const handleSearch = () => {
    if (searchQuery.trim()) {
      router.push(`/${locale}/tools?search=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-pulse">
          <div className="w-64 h-8 bg-accent rounded mb-4"></div>
          <div className="w-96 h-4 bg-accent rounded mb-2"></div>
          <div className="w-80 h-4 bg-accent rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-accent/5">
      <div className="container mx-auto px-4 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          {/* Error Icon and Code */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="mb-8"
          >
            <div className="inline-flex items-center justify-center w-24 h-24 rounded-full bg-destructive/10 text-destructive mb-4">
              <AlertTriangle size={48} />
            </div>
            <div className="text-6xl font-bold text-primary mb-2">404</div>
            <div className="text-sm text-muted-foreground font-mono">{t('errorCode')}</div>
          </motion.div>

          {/* Main Content */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="max-w-2xl mx-auto"
          >
            <h1 className="text-4xl font-bold text-foreground mb-4">
              {t('title')}
            </h1>
            <p className="text-xl text-muted-foreground mb-2">
              {t('subtitle')}
            </p>
            <p className="text-muted-foreground mb-8">
              {t('description')}
            </p>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="flex flex-wrap gap-4 justify-center mb-12"
          >
            <Button asChild size="lg" className="gap-2">
              <Link href={`/`}>
                <Home size={20} />
                {t('actions.goHome')}
              </Link>
            </Button>
            
            <Button asChild variant="outline" size="lg" className="gap-2">
              <Link href={`/tools`}>
                <Grid3X3 size={20} />
                {t('actions.browseTools')}
              </Link>
            </Button>

            <Button 
              variant="outline" 
              size="lg" 
              className="gap-2"
              onClick={() => router.back()}
            >
              <ArrowLeft size={20} />
              {t('actions.goBack')}
            </Button>
          </motion.div>

          {/* Search Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="max-w-md mx-auto mb-16"
          >
            <div className="relative">
              <input
                type="text"
                placeholder={t('actions.searchPlaceholder')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                className="w-full h-12 pl-4 pr-12 rounded-lg border border-border bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
              <button
                onClick={handleSearch}
                className="absolute right-3 top-3 p-1.5 rounded-md hover:bg-accent transition-colors"
              >
                <Search size={20} className="text-muted-foreground" />
              </button>
            </div>
          </motion.div>
        </motion.div>

        {/* Popular Tools Section */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0 }}
          className="mb-16"
        >
          <div className="text-center mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-2">
              {t('popularTools.title')}
            </h2>
            <p className="text-muted-foreground">
              {t('popularTools.subtitle')}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {popularTools.map((tool, index) => (
              <motion.div
                key={tool.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.2 + index * 0.1 }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer group">
                  <Link href={`/${locale}/tools/${tool.id}`}>
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center text-primary group-hover:bg-primary group-hover:text-white transition-colors">
                          {tool.icon}
                        </div>
                        <div>
                          <CardTitle className="text-lg">
                            {locale === 'en' ? (tool.en_name || tool.name) : (tool.zh_name || tool.name)}
                          </CardTitle>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="line-clamp-2">
                        {locale === 'en' ? (tool.en_description || tool.description) : (tool.zh_description || tool.description)}
                      </CardDescription>
                    </CardContent>
                  </Link>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Categories Section */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.4 }}
          className="mb-16"
        >
          <div className="text-center mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-2">
              {t('categories.title')}
            </h2>
            <p className="text-muted-foreground">
              {t('categories.subtitle')}
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {categories.map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.6 + index * 0.1 }}
              >
                <Link href={`/${locale}/tools?category=${category.id}`}>
                  <Card className="text-center hover:shadow-md transition-shadow cursor-pointer group">
                    <CardContent className="p-6">
                      <div className="text-3xl mb-2">{category.icon}</div>
                      <div className="text-sm font-medium text-foreground group-hover:text-primary transition-colors">
                        {category.name}
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Help Section */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.8 }}
          className="text-center"
        >
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center justify-center gap-2">
                <Compass size={24} />
                {t('suggestions.title')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-muted-foreground mb-6">
                <li>• {t('suggestions.checkUrl')}</li>
                <li>• {t('suggestions.goHome')}</li>
                <li>• {t('suggestions.searchTools')}</li>
                <li>• {t('suggestions.browseCategories')}</li>
                <li>• {t('suggestions.contactSupport')}</li>
              </ul>
              
              <Button asChild variant="outline" className="gap-2">
                <Link href={`/${locale}/contact`}>
                  <Mail size={20} />
                  {t('actions.contactUs')}
                </Link>
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
