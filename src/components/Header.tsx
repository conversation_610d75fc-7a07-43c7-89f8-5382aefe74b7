"use client";

import Link from "next/link";
import { But<PERSON> } from "./ui/button";
import { usePathname, useRouter } from "next/navigation";
import { locales, localeNames } from "@/i18n/routing";
import { Menu, Globe, Search } from "lucide-react";
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  She<PERSON><PERSON><PERSON>er,
  She<PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import { signIn, signOut, useSession } from "next-auth/react";
import Image from "next/image";
import { useState, useEffect, useRef } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tool,
  Notification,
  tools,
  createNotification,
  isToolDeveloped,
} from "@/lib/tools";
import { motion } from "framer-motion";
import { ThemeToggle } from "@/components/ThemeToggle";


interface HeaderProps {
  header: {
    logo: string;
    nav: {
      features: string;
      pricing: string;
      examples: string;
      docs: string;
    };
    cta: {
      login: string;
      signup: string;
    };
    userMenu: {
      myOrders: string;
      signOut: string;
    };
    search: {
      placeholder: string;
    };
  };
}

export default function Header({ header }: HeaderProps) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const router = useRouter();
  const currentLocale = pathname.split('/')[1];
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [mounted, setMounted] = useState(false);
  
  // 搜索相关状态
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [searchResults, setSearchResults] = useState<Tool[]>([]);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const searchRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 点击外部区域时隐藏搜索结果
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSearchResults(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [searchRef]);

  const switchLocale = (locale: string) => {
    const newPathname = pathname.replace(`/${currentLocale}`, `/${locale}`);
    window.location.href = newPathname;
  };
  
  // 添加通知
  const addNotification = (notification: Notification) => {
    setNotifications(prev => [...prev, notification]);
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== notification.id));
    }, 3000);
  };
  
  // 导航到工具
  const navigateToTool = (toolId: string) => {
    // 特殊处理分类导航，其他工具直接导航到详情页
    if (['all', 'favorites', 'recent'].includes(toolId)) {
      if (toolId === 'all') {
        router.push(`/${currentLocale}/tools`);
      } else {
        // 收藏夹和最近使用的工具可以跳转到过滤后的工具列表
        router.push(`/${currentLocale}/tools?filter=${toolId}`);
      }
    } else {
      // 检查工具是否已开发
      if (isToolDeveloped(toolId)) {
        router.push(`/${currentLocale}/tools/${toolId}`);
      } else {
        const tool = tools.find(t => t.id === toolId);
        if (tool) {
          addNotification(
            createNotification(
              '功能提示',
              `${tool.name} 功能正在开发中`,
              'info'
            )
          );
        }
      }
    }
  };
  
  // 处理搜索框的输入变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    
    // 如果搜索关键词为空，隐藏搜索结果
    if (!query.trim()) {
      setShowSearchResults(false);
      setSearchResults([]);
      return;
    }
    
    // 搜索匹配的工具
    const results = tools.filter(tool => {
      // 根据当前语言环境选择搜索字段
      const nameField = currentLocale === 'en' ? 
        (tool.en_name || tool.name) : 
        (tool.zh_name || tool.name);
      
      const descField = currentLocale === 'en' ? 
        (tool.en_description || tool.description) : 
        (tool.zh_description || tool.description);
      
      return nameField.toLowerCase().includes(query.toLowerCase()) || 
             descField.toLowerCase().includes(query.toLowerCase());
    });
    
    setSearchResults(results);
    setShowSearchResults(true);
  };
  
  // 处理搜索结果项点击
  const handleSearchResultClick = (toolId: string) => {
    setShowSearchResults(false);
    setSearchQuery(''); // 清空搜索框
    navigateToTool(toolId);
  };

  if (!mounted) {
    return (
      <header className="h-16 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="h-full px-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded bg-accent animate-pulse" />
            <div className="w-24 h-6 rounded bg-accent animate-pulse" />
          </div>
          <div className="flex-1 max-w-xl mx-4">
            <div className="w-full h-10 rounded-lg bg-accent animate-pulse" />
          </div>
          <div className="flex items-center gap-4">
            <div className="w-8 h-8 rounded bg-accent animate-pulse" />
          </div>
        </div>
      </header>
    );
  }

  return (
    <header className="h-16 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50">
      <div className="h-full px-4 sm:px-6 lg:px-8 flex items-center justify-between">
        {/* Logo - Enhanced visibility and sizing */}
        <Link href="/" className="flex items-center gap-3 hover:opacity-80 transition-opacity">
          <div className="relative">
            <Image
              src="/logo.jpg"
              alt="Toolbox Logo"
              width={40}
              height={40}
              className="rounded-lg shadow-sm ring-1 ring-border"
              priority
            />
            {/* Fallback icon if image fails to load */}
            <div className="absolute inset-0 flex items-center justify-center bg-primary/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity">
              <span className="text-primary font-bold text-lg">T</span>
            </div>
          </div>
          <div className="flex flex-col">
            <span className="text-xl font-bold text-foreground leading-tight">
              {header.logo}
            </span>
            <span className="text-xs text-muted-foreground hidden sm:block">
              {pathname.includes('/en') ? 'Online Tools' : '在线工具'}
            </span>
          </div>
        </Link>

        {/* Search Bar - Enhanced with better responsive design */}
        <div className="flex-1 max-w-2xl mx-4 hidden sm:block" ref={searchRef}>
          <div className="relative">
            <div className="relative">
              <input
                type="text"
                placeholder={header.search.placeholder}
                value={searchQuery}
                onChange={handleSearchChange}
                onFocus={(e) => {
                  // 在移动设备上，聚焦时滚动到输入框
                  if (window.innerWidth < 768) {
                    setTimeout(() => {
                      e.target.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }, 300);
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && searchQuery.trim()) {
                    setShowSearchResults(true);
                  }
                  if (e.key === 'Escape') {
                    setShowSearchResults(false);
                    e.currentTarget.blur();
                  }
                }}
                className="peer h-11 w-full rounded-full border border-border bg-background/50 backdrop-blur-sm text-foreground pl-5 pr-12 text-sm shadow-sm transition-all duration-200 hover:border-primary/50 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:shadow-md focus:bg-background"
                aria-label={header.search.placeholder}
                aria-expanded={showSearchResults}
                aria-haspopup="listbox"
                role="combobox"
                autoComplete="off"
              />
              <div
                onClick={() => {
                  const input = document.querySelector('input[aria-label="' + header.search.placeholder + '"]') as HTMLInputElement;
                  if (input) {
                    input.focus();
                    // 如果输入框内有文字且被点击了搜索图标，则执行搜索
                    if (searchQuery.trim()) {
                      setShowSearchResults(true);
                    }
                  }
                }}
                className="absolute right-4 top-1/2 -translate-y-1/2 h-4 w-4 cursor-pointer text-muted-foreground hover:text-primary transition-colors duration-200"
              >
                <Search className="w-full h-full" />
              </div>

              {/* 简化输入提示文字 - 仅在桌面端显示 */}
              <div className="absolute -top-8 left-6 bg-primary text-primary-foreground text-xs py-1.5 px-3 rounded-md opacity-0 scale-95 peer-focus:opacity-100 peer-focus:scale-100 transition-all duration-200 pointer-events-none hidden lg:block shadow-lg">
                {currentLocale === 'en' ? 'Enter keywords to search tools' : '输入关键词搜索工具'}
                <div className="absolute bottom-0 left-3 w-2 h-2 bg-primary rotate-45 translate-y-1"></div>
              </div>
            </div>

            {/* 搜索结果下拉框 */}
            {showSearchResults && searchResults.length > 0 && (
              <div
                className="absolute mt-2 w-full bg-card rounded-lg shadow-lg border border-border overflow-hidden z-50"
                role="listbox"
                aria-label={currentLocale === 'en' ? 'Search results' : '搜索结果'}
              >
                <div className="max-h-[60vh] overflow-y-auto">
                  <div className="p-3 text-xs text-muted-foreground font-medium border-b border-border">
                    {currentLocale === 'en'
                      ? `Found ${searchResults.length} matching results`
                      : `找到 ${searchResults.length} 个匹配结果`}
                  </div>
                  {searchResults.map((tool) => (
                    <div
                      key={tool.id}
                      className="px-4 py-3 hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors border-b border-border flex items-center gap-3 focus:bg-accent focus:text-accent-foreground focus:outline-none"
                      onClick={() => handleSearchResultClick(tool.id)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          handleSearchResultClick(tool.id);
                        }
                      }}
                      role="option"
                      tabIndex={0}
                      aria-selected={false}
                    >
                      <div className="w-9 h-9 flex-shrink-0 rounded-md bg-primary/5 flex items-center justify-center text-primary">
                        {tool.icon}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm text-foreground">
                          {currentLocale === 'en' ? (tool.en_name || tool.name) : (tool.zh_name || tool.name)}
                        </div>
                        <div className="text-xs text-muted-foreground line-clamp-1">
                          {currentLocale === 'en' ? (tool.en_description || tool.description) : (tool.zh_description || tool.description)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* 没有找到匹配结果 */}
            {showSearchResults && searchResults.length === 0 && searchQuery.trim() !== '' && (
              <div className="absolute mt-2 w-full bg-card rounded-lg shadow-lg border border-border overflow-hidden z-50">
                <div className="p-6 text-center">
                  <div className="text-sm font-medium text-foreground">
                    {currentLocale === 'en' ? 'No matching tools found' : '未找到匹配工具'}
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {currentLocale === 'en' ? 'Try searching with different keywords' : '尝试使用其他关键词搜索'}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Actions */}
        <div className="flex items-center space-x-2 sm:space-x-4">
          {/* Mobile Search Button */}
          <button
            className="sm:hidden p-2 rounded-lg hover:bg-accent transition-colors"
            onClick={() => {
              // Toggle mobile search modal or redirect to search page
              router.push(`/${currentLocale}/search`);
            }}
          >
            <Search className="h-5 w-5" />
          </button>

          {/* Theme Toggle */}
          <div className="hidden sm:block">
            <ThemeToggle />
          </div>

          {/* Language Selector */}
          <DropdownMenu>
            <DropdownMenuTrigger className="flex items-center space-x-1 text-sm text-foreground hover:text-primary transition-colors p-2 rounded-lg hover:bg-accent">
              <Globe className="h-4 w-4" />
              <span className="hidden sm:inline">{localeNames[currentLocale]}</span>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {locales.map((locale) => (
                <DropdownMenuItem
                  key={locale}
                  onClick={() => switchLocale(locale)}
                  className="cursor-pointer"
                >
                  <span className="flex items-center justify-between w-full">
                    {localeNames[locale]}
                    {locale === currentLocale && (
                      <span className="text-primary">✓</span>
                    )}
                  </span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* User Menu */}
          {session ? (
            <div className="relative" ref={dropdownRef}>
              <button
                type="button"
                className="flex items-center gap-2 p-2 rounded-lg hover:bg-accent cursor-pointer transition-colors"
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              >
                {session.user?.image ? (
                  <Image
                    className="h-8 w-8 rounded-full ring-2 ring-border"
                    src={session.user.image}
                    alt={session.user.name || ''}
                    width={32}
                    height={32}
                    unoptimized
                    priority
                  />
                ) : (
                  <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center font-medium">
                    {session.user?.name?.charAt(0) || '?'}
                  </div>
                )}
                <span className="text-sm font-medium hidden md:inline">{session.user?.name}</span>
              </button>

              {isDropdownOpen && (
                <div className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-lg bg-card border border-border shadow-lg">
                  <div className="px-4 py-3 text-sm border-b border-border">
                    <div className="font-medium text-foreground">{session.user?.name}</div>
                    <div className="text-muted-foreground truncate">{session.user?.email}</div>
                  </div>
                  <div className="py-1">
                    <Link
                      href={`/${currentLocale}/orders`}
                      className="flex items-center px-4 py-2 text-sm text-foreground hover:bg-accent hover:text-accent-foreground transition-colors"
                    >
                      {header.userMenu.myOrders}
                    </Link>
                    <button
                      type="button"
                      onClick={() => signOut({ callbackUrl: `/${currentLocale}` })}
                      className="flex items-center w-full px-4 py-2 text-left text-sm text-foreground hover:bg-accent hover:text-accent-foreground transition-colors"
                    >
                      {header.userMenu.signOut}
                    </button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <Button
              onClick={() => signIn()}
              size="sm"
              className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-4"
            >
              {header.cta.login}
            </Button>
          )}

          {/* Mobile Menu Button */}
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="sm" className="sm:hidden p-2">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-80">
              <SheetHeader>
                <SheetTitle className="text-left">
                  {currentLocale === 'en' ? 'Menu' : '菜单'}
                </SheetTitle>
              </SheetHeader>
              <div className="mt-6 space-y-4">
                {/* Mobile Search */}
                <div className="relative">
                  <input
                    type="text"
                    placeholder={header.search.placeholder}
                    className="w-full h-11 rounded-lg border border-border bg-background px-4 pr-10 text-sm focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20"
                  />
                  <Search className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                </div>

                {/* Mobile Theme Toggle */}
                <div className="flex items-center justify-between py-2">
                  <span className="text-sm font-medium">
                    {currentLocale === 'en' ? 'Theme' : '主题'}
                  </span>
                  <ThemeToggle />
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      {/* 通知 */}
      <div className="fixed bottom-4 right-4 space-y-2 z-50">
        {notifications.map((notification) => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className={`p-4 rounded-lg shadow-lg ${
              notification.type === 'success' ? 'bg-green-500' :
              notification.type === 'error' ? 'bg-red-500' :
              'bg-blue-500'
            } text-white`}
          >
            <h4 className="font-medium">{notification.title}</h4>
            <p className="text-sm">{notification.message}</p>
          </motion.div>
        ))}
      </div>
    </header>
  );
}