{"template": "flux-ai-template", "theme": "dark", "header": {"logo": "Bili-tool.com", "nav": {"introduce": "Features", "benefit": "Benefits", "showcase": "Showcase", "stats": "Stats", "pricing": "Pricing", "testimonial": "Testimonials", "faq": "FAQ"}, "cta": {"login": "<PERSON><PERSON>", "signup": "Get Started"}, "userMenu": {"myOrders": "My Orders", "signOut": "Sign Out"}, "search": {"placeholder": "Search Bili tools, keywords..."}}, "hero": {"title": "<PERSON><PERSON>l - Your All-in-One Online Toolbox", "subtitle": "Transform Your Daily Tasks with Bili Tool's Comprehensive Collection", "description": "State-of-the-art online toolbox platform offering a wide range of efficient tools for image processing, text editing, format conversion, and more", "cta": {"primary": "Start For Free", "secondary": "Explore Tools"}}, "branding": {"title": "Trusted by Users Worldwide", "subtitle": "Join thousands of satisfied users already using Bili Tool", "brands": [{"name": "Company 1", "logo": "/brands/1.svg"}, {"name": "Company 2", "logo": "/brands/2.svg"}, {"name": "Company 3", "logo": "/brands/3.svg"}]}, "introduce": {"title": "Advanced Online Tools Collection", "subtitle": "Powerful Features for Everyday Tasks", "description": "Bili-tool.com provides a comprehensive suite of online utilities designed to streamline your workflow and boost productivity", "image": "/features/intro.jpg", "features": [{"title": "Image Processing Tools", "description": "Powerful image editing, conversion, and optimization utilities to handle all your visual content needs", "icon": "sparkles"}, {"title": "Text and Data Processing", "description": "Efficient text editing, formatting, and data conversion tools for your content management tasks", "icon": "wand"}, {"title": "Developer Utilities", "description": "Essential development tools including formatters, validators, and code converters for programmers", "icon": "palette"}]}, "benefit": {"title": "Why Choose Us", "subtitle": "Benefits that set us apart", "benefits": [{"title": "Increased Productivity", "description": "Build and deploy faster with automated workflows", "icon": "speed"}, {"title": "Better Collaboration", "description": "Work together seamlessly with built-in collaboration tools", "icon": "team"}, {"title": "Enhanced Security", "description": "Enterprise-grade security to protect your code and data", "icon": "shield"}]}, "usage": {"title": "How It Works", "subtitle": "Get started in three simple steps", "steps": [{"title": "Create Your Project", "description": "Set up your project with our intuitive project creation wizard", "image": "/usage/step1.png"}, {"title": "Configure <PERSON><PERSON>s", "description": "Customize your project settings to match your needs", "image": "/usage/step2.png"}, {"title": "Start Development", "description": "Begin coding with our powerful development tools", "image": "/usage/step3.png"}]}, "feature": {"title": "Available Models", "subtitle": "Choose the perfect model for your needs", "items": [{"title": "Flux Pro", "description": "Flagship model optimized for high-end commercial use", "icon": "star"}, {"title": "Flux 1.1 Pro Ultra", "description": "4K resolution generator optimized for high-speed generation", "icon": "bolt"}, {"title": "Flux Schnell", "description": "Turbo mode for fastest image generation", "icon": "rocket"}]}, "showcase": {"title": "See It in Action", "subtitle": "Real projects built with our platform", "gallery": [{"title": "Project 1", "description": "A modern web application", "image": "/gallery/1.jpg"}, {"title": "Project 2", "description": "Mobile app development", "image": "/gallery/2.jpg"}, {"title": "Project 3", "description": "Enterprise solution", "image": "/gallery/3.jpg"}]}, "stats": {"title": "By the Numbers", "subtitle": "Our impact in the development community", "stats": [{"value": "10M+", "label": "Downloads", "description": "Active developers using our platform"}, {"value": "50K+", "label": "Projects", "description": "Successfully completed projects"}, {"value": "99.9%", "label": "Uptime", "description": "Platform reliability"}, {"value": "24/7", "label": "Support", "description": "Always here to help"}]}, "pricing": {"title": "Choose Your Plan", "subtitle": "Start creating amazing images today", "perMonth": "/month", "contactUs": "Contact Us", "getStarted": "Get Started", "buyNow": "Buy Now", "pleaseLogin": "Please log in to continue with your purchase", "plans": [{"name": "Basic", "price": "$9.99", "amount": 9.99, "description": "Perfect for individuals", "features": ["100 images per month", "Basic image generation", "Email support", "HD resolution (1024x1024)", "Access to Flux Basic model"]}, {"name": "Pro", "price": "$29.99", "amount": 29.99, "description": "For creative professionals", "features": ["1000 images per month", "Advanced image generation", "Priority support", "4K resolution", "Access to all Flux models", "API access"]}, {"name": "Enterprise", "price": "Contact Us", "description": "For large organizations", "features": ["Unlimited images", "Enterprise features", "Dedicated account manager", "Custom API integration", "SLA guarantee", "Advanced security features"]}]}, "testimonial": {"title": "What Our Users Say", "subtitle": "Don't just take our word for it", "testimonials": [{"content": "This platform has completely transformed how we create content. The productivity gains are incredible.", "author": {"name": "<PERSON>", "title": "Technical Director", "company": "Tech Company", "image": "/testimonials/1.jpg"}}]}, "faq": {"title": "Frequently Asked Questions", "subtitle": "Find answers to common questions", "faqs": [{"question": "How do I get started?", "answer": "Sign up for a free account and follow our quick start guide. You'll be up and running in minutes."}, {"question": "What kind of support do you offer?", "answer": "We offer various support options including community forums, documentation, and dedicated support for paid plans."}, {"question": "Can I integrate with existing tools?", "answer": "Yes! Our platform integrates with most popular development tools and services."}]}, "auth": {"signInTitle": "Sign in to your account", "signInWithGoogle": "Continue with Google", "signInWithGithub": "Continue with GitHub", "signInWithWechat": "Sign in with WeChat", "signingIn": "Signing in...", "oauthError": "An error occurred during sign in. Please try again.", "authError": "Authentication error. Please try again."}, "notFound": {"title": "Page Not Found", "subtitle": "Oops! The page you're looking for doesn't exist.", "description": "The page you are trying to access might have been moved, deleted, or you may have entered an incorrect URL.", "errorCode": "Error 404", "suggestions": {"title": "Here's what you can do:", "checkUrl": "Check the URL for any typos", "goHome": "Go back to the homepage", "searchTools": "Search for tools you need", "browseCategories": "Browse tool categories", "contactSupport": "Contact our support team"}, "actions": {"goHome": "Go to Homepage", "browseTools": "<PERSON><PERSON><PERSON>ls", "searchPlaceholder": "Search for tools...", "contactUs": "Contact Support", "goBack": "Go Back"}, "popularTools": {"title": "Popular Tools", "subtitle": "Try these frequently used tools"}, "categories": {"title": "Tool Categories", "subtitle": "Explore tools by category"}}, "cta": {"title": "Ready to Get Started?", "subtitle": "Join thousands of developers already using our platform", "cta": {"primary": "Start Free Trial", "secondary": "Contact Sales"}}, "footer": {"copyright": "All rights reserved", "about": {"title": "About", "about": "About Us", "blog": "Blog"}, "support": {"title": "Support", "helpCenter": "Help Center", "contactUs": "Contact Us"}, "legal": {"title": "Legal", "privacy": "Privacy Policy", "terms": "Terms of Service"}, "language": {"title": "Language", "english": "English", "chinese": "中文", "japanese": "日本語"}, "social": {"title": "Follow Us", "github": "GitHub", "twitter": "Twitter", "linkedin": "LinkedIn"}}, "orders": {"title": "My Orders", "description": "View your order history and payment status", "noOrders": "You haven't placed any orders yet", "orderDetails": {"purchase": "Purchase", "orderId": "Order ID", "amount": "Amount", "orderDate": "Order Date", "paidDate": "Payment Date"}}, "tools": {"common": {"allTools": "All Tools", "favorites": "Favorites", "recentUsed": "Recently Used", "searchPlaceholder": "Search for tools...", "noToolsFound": "No tools found", "tryDifferentKeywords": "Try different keywords or select another category", "toolsCount": "{{count}} tools"}, "ui": {"categoryTitle": "Tool Categories", "noCategories": "No Categories", "favorite": "Favorite", "addToFavorites": "Add to Favorites", "removeFromFavorites": "Remove from Favorites", "toolDetails": "Tool Details", "back": "Back", "share": "Share", "copy": "Copy", "copied": "Copied!", "loading": "Loading..."}, "categories": {"commonTools": "Common Tools", "jsonTools": "JSON Tools", "yamlTools": "YAML Tools", "securityTools": "Security Tools", "domainIp": "Domain/IP", "devTools": "Developer Tools", "utilityTools": "Utility Tools", "designTools": "Design Tools", "textProcessing": "Text Processing", "encodingTools": "Encoding/Decoding", "imageProcessing": "Image Processing", "videoAudio": "Video & Audio"}, "subcategories": {"hashTools": "Hash Calculation", "encryptionTools": "Encryption Tools", "passwordTools": "Password Tools", "securityCheck": "Security Check", "textEncode": "Text Encoding", "mediaEncode": "Media Encoding", "devEncode": "Development Encoding", "serverEncode": "Server Tools"}, "whois": {"name": "<PERSON><PERSON>", "description": "Query domain registration information, including owner, registrar, registration date and expiration date"}, "ip-location": {"name": "IP Geolocation", "description": "Lookup geographic location from IP address, including country, city, latitude/longitude and ISP information"}, "dns-lookup": {"name": "DNS Lookup", "description": "Query DNS records for a domain, including A, AAAA, MX, TXT, NS and other record types"}, "domain-info": {"name": "Domain Information", "description": "Comprehensive domain analysis, including server type, HTTP headers, SSL certificate details and more"}, "json-formatter": {"name": "JSON Formatter", "description": "Format and beautify JSON data to improve readability, with indentation and syntax highlighting"}, "json-js-convert": {"name": "JSON ⟷ JS Converter", "description": "Convert between JSON and JavaScript objects, with formatting and syntax highlighting"}, "qrcode": {"title": "QR Code Generator", "toolDescription": {"title": "Tool Description", "content": "This tool allows you to generate QR codes with customizable size, color, and margins. The generated QR code can be downloaded directly as a PNG image. All processing is done locally in your browser, and your data is never uploaded."}, "configOptions": {"title": "Configuration Options", "inputText": {"label": "Input Text", "placeholder": "Enter text to generate QR code..."}, "size": {"label": "QR Code Size", "pixels": "pixels"}, "color": {"foreground": "Foreground Color", "background": "Background Color"}, "includeMargin": "Include Margin"}, "preview": {"title": "QR Code Preview", "placeholder": "QR code will be displayed here..."}, "buttons": {"loadExample": "Load Example", "clear": "Clear", "download": "Download QR Code"}}, "imgCompress": {"title": "Image Compression Tool", "toolDescription": {"title": "Tool Description", "content": "A powerful online image compression tool that helps you reduce image file sizes while maintaining image quality. Supports multiple image formats and provides flexible compression options."}, "compressionSettings": {"title": "Compression Settings", "quality": "Compression Quality", "lowQuality": "Low Quality", "highQuality": "High Quality", "maxWidth": "<PERSON>", "maxHeight": "Max Height", "format": "Output Format", "maintainAspectRatio": "Maintain Aspect Ratio"}, "uploadArea": {"title": "Upload Images", "supportedFormats": "Supports common image formats like JPG, PNG, WebP"}, "fileList": {"title": "File List", "removeAll": "Clear All"}, "buttons": {"startCompression": "Start Compression", "processing": "Processing...", "downloadCompressed": "Download Compressed Image"}, "results": {"title": "Compression Results", "originalSize": "Original Size", "compressedSize": "Compressed Size", "compressionRatio": "Compression Ratio", "qualitySetting": "Quality Setting", "compressedImageAlt": "Compressed image of {filename}"}, "errors": {"invalidFiles": "Please upload valid image files", "noFiles": "Please upload image files first", "compressionFailed": "Compression failed, please try again", "imageLoadFailed": "Failed to load image", "fileReadFailed": "Failed to read file", "canvasContextFailed": "Failed to create canvas context", "compressionError": "An error occurred during compression"}}, "aesEncryption": {"title": "AES Encryption/Decryption", "description": "Online AES algorithm encryption and decryption tool, supporting various key lengths and encryption modes", "navigation": {"back": "Back to Tools"}, "modes": {"encrypt": "Encrypt", "decrypt": "Decrypt"}, "settings": {"title": "AES Settings", "keySize": {"label": "Key Size", "128bit": "128-bit", "192bit": "192-bit", "256bit": "256-bit"}, "mode": {"label": "Encryption Mode"}, "padding": {"label": "Padding Mode", "noPadding": "No Padding"}}, "input": {"encryptTitle": "Text to Encrypt", "decryptTitle": "Text to Decrypt", "encryptPlaceholder": "Enter text to encrypt...", "decryptPlaceholder": "Enter text to decrypt..."}, "output": {"encryptTitle": "Encryption Result", "decryptTitle": "Decryption Result", "encryptEmpty": "Encryption result will appear here", "decryptEmpty": "Decryption result will appear here", "clickButton": "Click the button below to start {action}"}, "key": {"title": "Key (Hex format)", "placeholder": "Enter hexadecimal key...", "requiredLength": "{size}-bit key requires {chars} hexadecimal characters"}, "iv": {"title": "Initialization Vector (IV) (Hex format)", "placeholder": "Enter hexadecimal initialization vector...", "requiredLength": "IV requires 32 hexadecimal characters (16 bytes)"}, "buttons": {"clear": "Clear", "loadExample": "Load Example", "copy": "Copy", "copyResult": "<PERSON><PERSON> Result", "generateKey": "Generate Random Key", "generateIv": "Generate Random IV"}, "processing": {"encrypting": "Encrypting...", "decrypting": "Decrypting..."}, "errors": {"emptyEncryptText": "Please enter text to encrypt", "emptyDecryptText": "Please enter text to decrypt", "emptyKey": "Please enter a key", "emptyIv": "Current mode requires an initialization vector (IV)", "encryptError": "Encryption error", "decryptError": "Decryption error", "unknownEncryptError": "Unknown error occurred during encryption", "unknownDecryptError": "Unknown error occurred during decryption"}, "examples": {"encryptText": "This is sensitive information that needs to be encrypted. Keep the key safe!", "decryptText": "aXcFkL9V1rxIB5xAEJHZCvpVH40D3+9x7wKGZ9lwpeI="}, "info": {"title": "About AES Encryption", "features": {"title": "AES Algorithm Features", "items": "Advanced Encryption Standard|US National Standard, replaced DES as the global symmetric encryption standard|Supports 128-bit, 192-bit, and 256-bit key lengths|Symmetric encryption algorithm, same key used for encryption and decryption|Highly secure, no practical breaking method exists|Efficient execution, suitable for large data encryption"}, "modes": {"title": "Encryption Mode Explanation", "items": "ECB (Electronic Codebook): Simplest mode, no IV required, but lower security|CBC (Cipher Block Chaining): Requires IV, each block related to previous block's encryption result|CFB (Cipher Feedback): Requires IV, converts block cipher to stream cipher|OFB (Output Feedback): Requires IV, generates keystream for encryption|CTR (Counter): Requires IV, counter mode that converts block cipher to stream cipher"}, "tips": {"title": "Security Usage Tips:", "items": "Use sufficiently long (256-bit) random keys to enhance security|For CBC and other modes, ensure unique initialization vectors (IV)|Avoid using ECB mode for important data|Key management is crucial; lost keys mean unrecoverable data|For practical applications, GCM or CCM authenticated encryption modes are recommended|Remember, the main challenge of symmetric encryption is key distribution|Use secure channels to transmit keys and IVs"}}}, "desEncryption": {"title": "DES Encryption/Decryption", "description": "Online DES algorithm encryption and decryption tool, supporting various encryption modes and padding methods", "navigation": {"back": "Back to Tools"}, "modes": {"encrypt": "Encrypt", "decrypt": "Decrypt"}, "settings": {"title": "DES Settings", "variant": {"label": "DES Algorithm Variant", "des": "DES (56-bit)", "des3": "3DES (168-bit)"}, "mode": {"label": "Encryption Mode"}, "padding": {"label": "Padding Mode", "noPadding": "No Padding"}}, "input": {"encryptTitle": "Text to Encrypt", "decryptTitle": "Text to Decrypt", "encryptPlaceholder": "Enter text to encrypt...", "decryptPlaceholder": "Enter text to decrypt..."}, "output": {"encryptTitle": "Encryption Result", "decryptTitle": "Decryption Result", "encryptEmpty": "Encryption result will appear here", "decryptEmpty": "Decryption result will appear here", "clickButton": "Click the button below to start {action}"}, "key": {"title": "Key (Hex format)", "placeholder": "Enter hexadecimal key...", "requiredLength": {"des": "DES requires 16 hexadecimal characters (8 bytes)", "des3": "3DES requires 48 hexadecimal characters (24 bytes)"}}, "iv": {"title": "Initialization Vector (IV) (Hex format)", "placeholder": "Enter hexadecimal initialization vector...", "requiredLength": "IV requires 16 hexadecimal characters (8 bytes)"}, "buttons": {"clear": "Clear", "loadExample": "Load Example", "copy": "Copy", "copyResult": "<PERSON><PERSON> Result", "generateKey": "Generate Random Key", "generateIv": "Generate Random IV"}, "processing": {"encrypting": "Encrypting...", "decrypting": "Decrypting..."}, "errors": {"emptyEncryptText": "Please enter text to encrypt", "emptyDecryptText": "Please enter text to decrypt", "emptyKey": "Please enter a key", "emptyIv": "Current mode requires an initialization vector (IV)", "encryptError": "Encryption error", "decryptError": "Decryption error", "unknownEncryptError": "Unknown error occurred during encryption", "unknownDecryptError": "Unknown error occurred during decryption"}, "examples": {"encryptText": "This is sensitive information that needs to be encrypted using DES!", "decryptText": "U2FsdGVkX1+XrNkJpK9+5zfI0AEPC+rfYVYI0n4KRz0jKJJ7PL19Qw=="}, "info": {"title": "About DES Encryption", "features": {"title": "DES Algorithm Features", "items": "Data Encryption Standard|Introduced in 1977 by FIPS (Federal Information Processing Standards)|Uses a 56-bit key length|Due to its short key length, it's no longer recommended for high-security needs|Triple DES (3DES) provides enhanced security"}, "modes": {"title": "Encryption Mode Explanation", "items": "ECB (Electronic Codebook): Simple but less secure|CBC (Cipher Block Chaining): Each block related to previous block's result|CFB (Cipher Feedback): Converts block cipher to stream cipher|OFB (Output Feedback): Generates keystream for encryption"}, "security": {"title": "Security Considerations", "items": "DES is considered insecure due to its short key length (56 bits)|For high-security applications, 3DES or AES is recommended|While 3DES is more secure than DES, it's slower|AES has become the preferred symmetric encryption algorithm in modern applications"}}}, "ipLocation": {"title": "IP Location", "description": "Query geographic location by IP address, including country, city, coordinates, and ISP information", "navigation": {"back": "Back to Tools"}, "form": {"ipAddress": "IP Address", "placeholder": "Enter IPv4 or IPv6 address, e.g.: *******", "submit": "Query", "searching": "Searching..."}, "errors": {"enterIpAddress": "Please enter an IP address", "invalidIpAddress": "Please enter a valid IP address (IPv4 or IPv6)", "invalidIpv4Range": "Invalid IPv4 address format, values should be between 0-255", "queryFailed": "Query failed", "retryLater": "Query failed, please try again later"}, "results": {"title": "Query Results", "actions": {"refresh": "Refresh", "copy": "Copy to Clipboard", "download": "Download Results"}, "geoLocation": {"title": "Geographic Location", "country": "Country", "registeredCountry": "Registered Country", "region": "Region", "latitude": "Latitude", "longitude": "Longitude", "timezone": "Timezone"}, "chinaIpInfo": {"title": "China IP Lookup Results", "isp": "ISP", "networkType": "Network Type", "province": "Province", "city": "City", "district": "District", "areaCode": "Area Code"}, "basicInfo": {"title": "Basic Information", "ipAddress": "IP Address", "subnet": "Subnet", "networkType": "Network Type"}, "ispInfo": {"title": "ISP Information", "provider": "Provider", "asn": "ASN", "organization": "Organization"}, "disclaimer": "Note: IP geolocation information is for reference only and its accuracy may be affected by various factors."}, "instructions": {"title": "Instructions", "steps": ["Enter the IP address you want to look up (e.g.: *******) in the input field", "Click the \"Query\" button to retrieve geographic location information for the IP address", "The results will display the country/region, city, ISP provider, and other information for the IP", "You can use the toolbar buttons to copy or download the query results"], "tip": "Tip: This tool supports both IPv4 and IPv6 address lookup. Due to the complexity of IP address allocation and usage, location information for some IP addresses may be inaccurate or only show an approximate area."}}, "img-resize": {"title": "Image Resize", "description": "Resize images by pixels or percentage while maintaining quality", "resizeByPixel": "Resize by <PERSON><PERSON><PERSON>", "resizeByPercentage": "Resize by Percentage", "presetSize": "Preset Size", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "percentage": "Percentage", "resize": "Resize", "dragAndDrop": "Drag and drop image here", "orClickToSelect": "or click to select", "error": {"invalidFile": "Invalid File", "pleaseUploadImage": "Please upload an image file"}, "originalSize": "Original Size", "newSize": "New Size", "download": "Download Resized Image", "supportedFormats": "Supported formats: PNG, JPG, JPEG, GIF, WebP", "success": {"title": "Resize Successful", "description": "Image has been resized and download started"}, "presetSizeDescription": "Preset Size", "presetSizeNote": "Note: Aspect ratio will be maintained, small images will not be enlarged", "maintainAspectRatio": "Maintain Aspect Ratio", "presetSizes": {"4:3": "4:3 Landscape", "3:4": "3:4 Portrait", "16:9": "16:9 Widescreen", "9:16": "9:16 Portrait", "custom": "Custom Size"}}}, "privacy": {"title": "Privacy Policy", "lastUpdated": "Last Updated: December 1, 2023", "backToHome": "Back to Home", "content": {"welcome": "Welcome to Online Toolbox (hereinafter referred to as 'we', 'this website'). We take your privacy and personal information protection very seriously. This privacy policy aims to help you understand how we collect, use, store, and protect your personal information, as well as your related rights. Please read this privacy policy carefully before using our services.", "sections": {"collection": {"title": "1. Information Collection", "intro": "When you use our tools and services, we may collect the following types of information:", "items": ["Information you actively provide: including personal information such as name, email address, etc., that you provide when registering an account, using specific tools, contacting us, or participating in surveys.", "Automatically collected information: When you visit or use our website, we automatically collect technical data such as IP address, browser type, device information, and access time.", "Tool usage data: We record how you use our tools, such as queried domains, IP addresses, or other related data, to improve our services."]}, "usage": {"title": "2. Information Usage", "intro": "We use the collected information primarily for the following purposes:", "items": ["Providing, maintaining, and improving our tools and services", "Processing your requests and responding to your inquiries", "Sending important notifications, such as service terms changes or policy updates", "Sending marketing and promotional information with your consent", "Preventing fraud and abuse, ensuring website security", "Conducting data analysis and research to improve user experience"]}, "sharing": {"title": "3. Information Sharing", "intro": "We respect your privacy and will not sell, trade, or rent your personal information to third parties. However, we may share your information in the following circumstances:", "items": ["Sharing with your explicit consent", "Sharing with our service providers and partners to assist us in providing services", "Legal requirements or legitimate requests from government agencies", "Protecting our rights, property, or safety, as well as the rights, property, or safety of our users or the public"]}, "security": {"title": "4. Data Security", "content": "We implement appropriate technical and organizational measures to protect your personal information from unauthorized access, use, disclosure, or damage. Although we strive to protect your personal information, internet transmission and electronic storage are not absolutely secure, and we cannot guarantee absolute security of information."}, "cookies": {"title": "5. Cookies and Similar Technologies", "content": "We use cookies and similar technologies to collect usage information, improve user experience, and remember your preferences. You can control cookies through browser settings, but this may affect the availability of some website features."}, "rights": {"title": "6. Your Rights", "intro": "Under applicable data protection laws, you may have the following rights:", "items": ["Right to access: Understand what personal information we process about you", "Right to rectification: Correct inaccurate or incomplete personal information", "Right to erasure: Request deletion of your personal information in specific circumstances", "Right to restrict processing: Restrict our processing of your personal information in specific circumstances", "Right to data portability: Receive your personal information in a structured, commonly used, and machine-readable format when technically feasible", "Right to object: Object to specific types of processing activities"], "outro": "To exercise these rights, please contact us through the contact information provided at the end of this policy."}, "children": {"title": "7. Children's Privacy", "content": "Our services are not intended for children under 13. If we discover that we have inadvertently collected personal information from children under 13, we will immediately delete such data. If you are a parent or guardian and discover that your child has provided us with personal information, please contact us."}, "thirdParty": {"title": "8. Third-Party Links", "content": "Our website may contain links to third-party websites. We are not responsible for the privacy policies or content of these websites. We recommend that you review the privacy policy of any third-party website before using it."}, "updates": {"title": "9. Policy Updates", "content": "We may update this privacy policy from time to time to reflect changes in our practices or legal requirements. Updated policies will be posted on the website, and significant changes will be notified to you through appropriate means. We recommend that you regularly review this policy to stay informed."}, "contact": {"title": "10. Contact Us", "intro": "If you have any questions, comments, or complaints about this privacy policy, please contact us through the following means:", "email": "Email: <EMAIL>", "address": "Mailing Address: No.1 Zhongguancun Street, Haidian District, Beijing", "phone": "Phone: +86-10-12345678", "outro": "We will respond to your request within 30 days of receipt."}}}}, "blog": {"title": "Blog Posts", "subtitle": "Explore our technical tutorials, product updates, and industry insights", "backToHome": "Back to Home", "search": {"placeholder": "Search articles..."}, "categories": {"all": "All", "tutorials": "Tutorials", "updates": "Updates", "news": "Industry News", "tips": "Tips & Tricks"}, "featured": {"label": "Featured Post"}, "post": {"readMore": "Read More", "readTime": "Read Time", "author": "Author", "date": "Published Date", "noResults": {"title": "No Articles Found", "description": "Try different search terms or browse other categories", "action": "View All Articles"}}}}